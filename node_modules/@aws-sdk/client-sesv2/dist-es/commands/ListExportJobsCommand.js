import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_ListExportJobsCommand, se_ListExportJobsCommand } from "../protocols/Aws_restJson1";
export { $Command };
export class ListExportJobsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("SimpleEmailService_v2", "ListExportJobs", {})
    .n("SESv2Client", "ListExportJobsCommand")
    .f(void 0, void 0)
    .ser(se_ListExportJobsCommand)
    .de(de_ListExportJobsCommand)
    .build() {
}
