{"version": 3, "file": "ConfigError.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "ConfigErrorTypeId", "exports", "And", "Or", "MissingData", "InvalidData", "SourceUnavailable", "Unsupported", "isConfigError", "isAnd", "isOr", "isInvalidData", "isMissingData", "isMissingDataOnly", "isSourceUnavailable", "isUnsupported", "prefixed", "reduceWithContext"], "sources": ["../../src/ConfigError.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAIA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAqD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAErD;;;;AAIO,MAAMkB,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAkBtB,QAAQ,CAACsB,iBAAiB;AAmI1E;;;;AAIO,MAAME,GAAG,GAAAD,OAAA,CAAAC,GAAA,GAA0DxB,QAAQ,CAACwB,GAAG;AAEtF;;;;AAIO,MAAMC,EAAE,GAAAF,OAAA,CAAAE,EAAA,GAA0DzB,QAAQ,CAACyB,EAAE;AAEpF;;;;AAIO,MAAMC,WAAW,GAAAH,OAAA,CAAAG,WAAA,GACtB1B,QAAQ,CAAC0B,WAAW;AAEtB;;;;AAIO,MAAMC,WAAW,GAAAJ,OAAA,CAAAI,WAAA,GACtB3B,QAAQ,CAAC2B,WAAW;AAEtB;;;;AAIO,MAAMC,iBAAiB,GAAAL,OAAA,CAAAK,iBAAA,GAKX5B,QAAQ,CAAC4B,iBAAiB;AAE7C;;;;AAIO,MAAMC,WAAW,GAAAN,OAAA,CAAAM,WAAA,GACtB7B,QAAQ,CAAC6B,WAAW;AAEtB;;;;;;AAMO,MAAMC,aAAa,GAAAP,OAAA,CAAAO,aAAA,GAAqC9B,QAAQ,CAAC8B,aAAa;AAErF;;;;;;AAMO,MAAMC,KAAK,GAAAR,OAAA,CAAAQ,KAAA,GAAuC/B,QAAQ,CAAC+B,KAAK;AAEvE;;;;;;AAMO,MAAMC,IAAI,GAAAT,OAAA,CAAAS,IAAA,GAAsChC,QAAQ,CAACgC,IAAI;AAEpE;;;;;;;AAOO,MAAMC,aAAa,GAAAV,OAAA,CAAAU,aAAA,GAA+CjC,QAAQ,CAACiC,aAAa;AAE/F;;;;;;;AAOO,MAAMC,aAAa,GAAAX,OAAA,CAAAW,aAAA,GAA+ClC,QAAQ,CAACkC,aAAa;AAE/F;;;;;;AAMO,MAAMC,iBAAiB,GAAAZ,OAAA,CAAAY,iBAAA,GAAmCnC,QAAQ,CAACmC,iBAAiB;AAE3F;;;;;;;AAOO,MAAMC,mBAAmB,GAAAb,OAAA,CAAAa,mBAAA,GAAqDpC,QAAQ,CAACoC,mBAAmB;AAEjH;;;;;;;AAOO,MAAMC,aAAa,GAAAd,OAAA,CAAAc,aAAA,GAA+CrC,QAAQ,CAACqC,aAAa;AAE/F;;;;AAIO,MAAMC,QAAQ,GAAAf,OAAA,CAAAe,QAAA,GAWjBtC,QAAQ,CAACsC,QAAQ;AAErB;;;;AAIO,MAAMC,iBAAiB,GAAAhB,OAAA,CAAAgB,iBAAA,GAW1BvC,QAAQ,CAACuC,iBAAiB", "ignoreList": []}