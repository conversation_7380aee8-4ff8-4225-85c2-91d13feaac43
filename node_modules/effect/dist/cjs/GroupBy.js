"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.make = exports.first = exports.filter = exports.evaluate = exports.GroupByTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/groupBy.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const GroupByTypeId = exports.GroupByTypeId = internal.GroupByTypeId;
/**
 * Run the function across all groups, collecting the results in an
 * arbitrary order.
 *
 * @since 2.0.0
 * @category destructors
 */
const evaluate = exports.evaluate = internal.evaluate;
/**
 * Filter the groups to be processed.
 *
 * @since 2.0.0
 * @category utils
 */
const filter = exports.filter = internal.filter;
/**
 * Only consider the first `n` groups found in the `Stream`.
 *
 * @since 2.0.0
 * @category utils
 */
const first = exports.first = internal.first;
/**
 * Constructs a `GroupBy` from a `Stream`.
 *
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
//# sourceMappingURL=GroupBy.js.map