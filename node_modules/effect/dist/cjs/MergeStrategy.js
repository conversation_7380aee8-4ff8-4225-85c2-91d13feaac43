"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.match = exports.isMergeStrategy = exports.isBufferSliding = exports.isBackPressure = exports.MergeStrategyTypeId = exports.BufferSliding = exports.BackPressure = void 0;
var internal = _interopRequireWildcard(require("./internal/channel/mergeStrategy.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

/**
 * @since 2.0.0
 * @category symbols
 */
const MergeStrategyTypeId = exports.MergeStrategyTypeId = internal.MergeStrategyTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const BackPressure = exports.BackPressure = internal.BackPressure;
/**
 * @since 2.0.0
 * @category constructors
 */
const BufferSliding = exports.BufferSliding = internal.BufferSliding;
/**
 * Returns `true` if the specified value is a `MergeStrategy`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isMergeStrategy = exports.isMergeStrategy = internal.isMergeStrategy;
/**
 * Returns `true` if the specified `MergeStrategy` is a `BackPressure`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isBackPressure = exports.isBackPressure = internal.isBackPressure;
/**
 * Returns `true` if the specified `MergeStrategy` is a `BufferSliding`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
const isBufferSliding = exports.isBufferSliding = internal.isBufferSliding;
/**
 * Folds an `MergeStrategy` into a value of type `A`.
 *
 * @since 2.0.0
 * @category folding
 */
const match = exports.match = internal.match;
//# sourceMappingURL=MergeStrategy.js.map