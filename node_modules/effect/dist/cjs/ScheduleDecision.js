"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.isDone = exports.isContinue = exports.done = exports.continueWith = exports.continue = void 0;
var internal = _interopRequireWildcard(require("./internal/schedule/decision.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */

const _continue = exports.continue = internal._continue;
/**
 * @since 2.0.0
 * @category constructors
 */
const continueWith = exports.continueWith = internal.continueWith;
/**
 * @since 2.0.0
 * @category constructors
 */
const done = exports.done = internal.done;
/**
 * @since 2.0.0
 * @category refinements
 */
const isContinue = exports.isContinue = internal.isContinue;
/**
 * @since 2.0.0
 * @category refinements
 */
const isDone = exports.isDone = internal.isDone;
//# sourceMappingURL=ScheduleDecision.js.map