{"version": 3, "file": "FiberStatus.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "FiberStatusTypeId", "exports", "done", "running", "suspended", "isFiberStatus", "isDone", "isRunning", "isSuspended"], "sources": ["../../src/FiberStatus.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAKA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAqD,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGrD;;;;AAIO,MAAMkB,iBAAiB,GAAAC,OAAA,CAAAD,iBAAA,GAAkBtB,QAAQ,CAACsB,iBAAiB;AA4C1E;;;;AAIO,MAAME,IAAI,GAAAD,OAAA,CAAAC,IAAA,GAAgBxB,QAAQ,CAACwB,IAAI;AAE9C;;;;AAIO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAA6DzB,QAAQ,CAACyB,OAAO;AAEjG;;;;AAIO,MAAMC,SAAS,GAAAH,OAAA,CAAAG,SAAA,GACpB1B,QAAQ,CAAC0B,SAAS;AAEpB;;;;;;AAMO,MAAMC,aAAa,GAAAJ,OAAA,CAAAI,aAAA,GAAqC3B,QAAQ,CAAC2B,aAAa;AAErF;;;;;;AAMO,MAAMC,MAAM,GAAAL,OAAA,CAAAK,MAAA,GAAwC5B,QAAQ,CAAC4B,MAAM;AAE1E;;;;;;;AAOO,MAAMC,SAAS,GAAAN,OAAA,CAAAM,SAAA,GAA2C7B,QAAQ,CAAC6B,SAAS;AAEnF;;;;;;;AAOO,MAAMC,WAAW,GAAAP,OAAA,CAAAO,WAAA,GAA6C9B,QAAQ,CAAC8B,WAAW", "ignoreList": []}