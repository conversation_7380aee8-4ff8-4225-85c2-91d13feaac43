{"version": 3, "file": "Equivalence.js", "names": ["_Function", "require", "make", "isEquivalent", "self", "that", "exports", "isStrictEquivalent", "x", "y", "strict", "string", "number", "boolean", "bigint", "symbol", "combine", "dual", "combineMany", "collection", "equivalence", "isAlwaysEquivalent", "_x", "_y", "combineAll", "mapInput", "f", "Date", "date", "getTime", "product", "xa", "xb", "ya", "yb", "all", "len", "Math", "min", "length", "collectionLength", "productMany", "slice", "tuple", "elements", "array", "item", "i", "isEq", "struct", "fields", "keys", "Object", "key"], "sources": ["../../src/Equivalence.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAOA,IAAAA,SAAA,GAAAC,OAAA;AAPA;;;;;;;;AA0BA;;;;AAIO,MAAMC,IAAI,GAAOC,YAA2C,IAAqB,CAACC,IAAO,EAAEC,IAAO,KACvGD,IAAI,KAAKC,IAAI,IAAIF,YAAY,CAACC,IAAI,EAAEC,IAAI,CAAC;AAAAC,OAAA,CAAAJ,IAAA,GAAAA,IAAA;AAE3C,MAAMK,kBAAkB,GAAGA,CAACC,CAAU,EAAEC,CAAU,KAAKD,CAAC,KAAKC,CAAC;AAE9D;;;;;;AAMO,MAAMC,MAAM,GAA4BA,CAAA,KAAMH,kBAAkB;AAEvE;;;;AAAAD,OAAA,CAAAI,MAAA,GAAAA,MAAA;AAIO,MAAMC,MAAM,GAAAL,OAAA,CAAAK,MAAA,gBAAwBD,MAAM,EAAE;AAEnD;;;;AAIO,MAAME,MAAM,GAAAN,OAAA,CAAAM,MAAA,gBAAwBF,MAAM,EAAE;AAEnD;;;;AAIO,MAAMG,OAAO,GAAAP,OAAA,CAAAO,OAAA,gBAAyBH,MAAM,EAAE;AAErD;;;;AAIO,MAAMI,MAAM,GAAAR,OAAA,CAAAQ,MAAA,gBAAwBJ,MAAM,EAAE;AAEnD;;;;AAIO,MAAMK,MAAM,GAAAT,OAAA,CAAAS,MAAA,gBAAwBL,MAAM,EAAE;AAEnD;;;;AAIO,MAAMM,OAAO,GAAAV,OAAA,CAAAU,OAAA,gBAWhB,IAAAC,cAAI,EAAC,CAAC,EAAE,CAAIb,IAAoB,EAAEC,IAAoB,KAAqBH,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKL,IAAI,CAACI,CAAC,EAAEC,CAAC,CAAC,IAAIJ,IAAI,CAACG,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC;AAExH;;;;AAIO,MAAMS,WAAW,GAAAZ,OAAA,CAAAY,WAAA,gBAWpB,IAAAD,cAAI,EAAC,CAAC,EAAE,CAAIb,IAAoB,EAAEe,UAAoC,KACxEjB,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAI;EACZ,IAAI,CAACL,IAAI,CAACI,CAAC,EAAEC,CAAC,CAAC,EAAE;IACf,OAAO,KAAK;EACd;EACA,KAAK,MAAMW,WAAW,IAAID,UAAU,EAAE;IACpC,IAAI,CAACC,WAAW,CAACZ,CAAC,EAAEC,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb,CAAC,CAAC,CAAC;AAEL,MAAMY,kBAAkB,GAAyBA,CAACC,EAAE,EAAEC,EAAE,KAAK,IAAI;AAEjE;;;;AAIO,MAAMC,UAAU,GAAOL,UAAoC,IAChED,WAAW,CAACG,kBAAkB,EAAEF,UAAU,CAAC;AAE7C;;;;AAAAb,OAAA,CAAAkB,UAAA,GAAAA,UAAA;AAIO,MAAMC,QAAQ,GAAAnB,OAAA,CAAAmB,QAAA,gBAWjB,IAAAR,cAAI,EACN,CAAC,EACD,CAAOb,IAAoB,EAAEsB,CAAc,KAAqBxB,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAKL,IAAI,CAACsB,CAAC,CAAClB,CAAC,CAAC,EAAEkB,CAAC,CAACjB,CAAC,CAAC,CAAC,CAAC,CACjG;AAED;;;;AAIO,MAAMkB,IAAI,GAAArB,OAAA,CAAAqB,IAAA,gBAAsBF,QAAQ,CAACb,MAAM,EAAGgB,IAAI,IAAKA,IAAI,CAACC,OAAO,EAAE,CAAC;AAEjF;;;;AAIO,MAAMC,OAAO,GAAAxB,OAAA,CAAAwB,OAAA,gBAGhB,IAAAb,cAAI,EACN,CAAC,EACD,CAAOb,IAAoB,EAAEC,IAAoB,KAC/CH,IAAI,CAAC,CAAC,CAAC6B,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAK9B,IAAI,CAAC2B,EAAE,EAAEE,EAAE,CAAC,IAAI5B,IAAI,CAAC2B,EAAE,EAAEE,EAAE,CAAC,CAAC,CAC7D;AAED;;;;AAIO,MAAMC,GAAG,GAAOhB,UAAoC,IAAmC;EAC5F,OAAOjB,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAI;IACnB,MAAM2B,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC9B,CAAC,CAAC+B,MAAM,EAAE9B,CAAC,CAAC8B,MAAM,CAAC;IAExC,IAAIC,gBAAgB,GAAG,CAAC;IACxB,KAAK,MAAMpB,WAAW,IAAID,UAAU,EAAE;MACpC,IAAIqB,gBAAgB,IAAIJ,GAAG,EAAE;QAC3B;MACF;MACA,IAAI,CAAChB,WAAW,CAACZ,CAAC,CAACgC,gBAAgB,CAAC,EAAE/B,CAAC,CAAC+B,gBAAgB,CAAC,CAAC,EAAE;QAC1D,OAAO,KAAK;MACd;MACAA,gBAAgB,EAAE;IACpB;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAED;;;;AAAAlC,OAAA,CAAA6B,GAAA,GAAAA,GAAA;AAIO,MAAMM,WAAW,GAAGA,CACzBrC,IAAoB,EACpBe,UAAoC,KACuC;EAC3E,MAAMC,WAAW,GAAGe,GAAG,CAAChB,UAAU,CAAC;EACnC,OAAOjB,IAAI,CAAC,CAACM,CAAC,EAAEC,CAAC,KAAK,CAACL,IAAI,CAACI,CAAC,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGW,WAAW,CAACZ,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC,EAAEjC,CAAC,CAACiC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC;AAED;;;;;;;;;;;;;AAAApC,OAAA,CAAAmC,WAAA,GAAAA,WAAA;AAaO,MAAME,KAAK,GAAGA,CACnB,GAAGC,QAAW,KACmFT,GAAG,CAACS,QAAQ,CAAQ;AAEvH;;;;;;AAAAtC,OAAA,CAAAqC,KAAA,GAAAA,KAAA;AAMO,MAAME,KAAK,GAAOC,IAAoB,IAC3C5C,IAAI,CAAC,CAACE,IAAI,EAAEC,IAAI,KAAI;EAClB,IAAID,IAAI,CAACmC,MAAM,KAAKlC,IAAI,CAACkC,MAAM,EAAE;IAC/B,OAAO,KAAK;EACd;EAEA,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,IAAI,CAACmC,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACpC,MAAMC,IAAI,GAAGF,IAAI,CAAC1C,IAAI,CAAC2C,CAAC,CAAC,EAAE1C,IAAI,CAAC0C,CAAC,CAAC,CAAC;IACnC,IAAI,CAACC,IAAI,EAAE;MACT,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb,CAAC,CAAC;AAEJ;;;;;;;AAAA1C,OAAA,CAAAuC,KAAA,GAAAA,KAAA;AAOO,MAAMI,MAAM,GACjBC,MAAS,IACsF;EAC/F,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,MAAM,CAAC;EAChC,OAAOhD,IAAI,CAAC,CAACE,IAAI,EAAEC,IAAI,KAAI;IACzB,KAAK,MAAMgD,GAAG,IAAIF,IAAI,EAAE;MACtB,IAAI,CAACD,MAAM,CAACG,GAAG,CAAC,CAACjD,IAAI,CAACiD,GAAG,CAAC,EAAEhD,IAAI,CAACgD,GAAG,CAAC,CAAC,EAAE;QACtC,OAAO,KAAK;MACd;IACF;IACA,OAAO,IAAI;EACb,CAAC,CAAC;AACJ,CAAC;AAAA/C,OAAA,CAAA2C,MAAA,GAAAA,MAAA", "ignoreList": []}