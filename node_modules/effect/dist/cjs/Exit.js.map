{"version": 3, "file": "Exit.js", "names": ["core", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "isExit", "exports", "exitIsExit", "isFailure", "exitIsFailure", "isSuccess", "exitIsSuccess", "isInterrupted", "exitIsInterrupted", "as", "exitAs", "asVoid", "exitAsVoid", "causeOption", "exitCauseOption", "all", "exitCollectAll", "die", "exitDie", "exists", "exitExists", "fail", "exitFail", "failCause", "exitFailCause", "flatMap", "exitFlatMap", "flatMapEffect", "exitFlatMapEffect", "flatten", "exitFlatten", "forEachEffect", "exitForEachEffect", "fromEither", "exitFromEither", "fromOption", "exitFromOption", "getOr<PERSON><PERSON>e", "exitGetOrElse", "interrupt", "exitInterrupt", "map", "exitMap", "mapBoth", "exitMapBoth", "mapError", "exitMapError", "mapErrorCause", "exitMapErrorCause", "match", "exitMatch", "matchEffect", "exitMatchEffect", "succeed", "exitSucceed", "void_", "void", "exitVoid", "zip", "exitZip", "zipLeft", "exitZipLeft", "zipRight", "exitZipRight", "zipPar", "exitZipPar", "zipParLeft", "exitZipParLeft", "zipParRight", "exitZipParRight", "zipWith", "exitZipWith"], "sources": ["../../src/Exit.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAQA,IAAAA,IAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA0C,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAuE1C;;;;;;AAMO,MAAMkB,MAAM,GAAAC,OAAA,CAAAD,MAAA,GAAgDtB,IAAI,CAACwB,UAAU;AAElF;;;;;;AAMO,MAAMC,SAAS,GAAAF,OAAA,CAAAE,SAAA,GAAsDzB,IAAI,CAAC0B,aAAa;AAE9F;;;;;;AAMO,MAAMC,SAAS,GAAAJ,OAAA,CAAAI,SAAA,GAAsD3B,IAAI,CAAC4B,aAAa;AAE9F;;;;;;;AAOO,MAAMC,aAAa,GAAAN,OAAA,CAAAM,aAAA,GAAwC7B,IAAI,CAAC8B,iBAAiB;AAExF;;;;;;;AAOO,MAAMC,EAAE,GAAAR,OAAA,CAAAQ,EAAA,GAiBX/B,IAAI,CAACgC,MAAM;AAEf;;;;;;AAMO,MAAMC,MAAM,GAAAV,OAAA,CAAAU,MAAA,GAA8CjC,IAAI,CAACkC,UAAU;AAEhF;;;;;;;AAOO,MAAMC,WAAW,GAAAZ,OAAA,CAAAY,WAAA,GAA8DnC,IAAI,CAACoC,eAAe;AAE1G;;;;;;;AAOO,MAAMC,GAAG,GAAAd,OAAA,CAAAc,GAAA,GAGwBrC,IAAI,CAACsC,cAAc;AAE3D;;;;;;AAMO,MAAMC,GAAG,GAAAhB,OAAA,CAAAgB,GAAA,GAAqCvC,IAAI,CAACwC,OAAO;AAEjE;;;;;;;AAOO,MAAMC,MAAM,GAAAlB,OAAA,CAAAkB,MAAA,GAiCfzC,IAAI,CAAC0C,UAAU;AAEnB;;;;;;;AAOO,MAAMC,IAAI,GAAApB,OAAA,CAAAoB,IAAA,GAAoC3C,IAAI,CAAC4C,QAAQ;AAElE;;;;;;AAMO,MAAMC,SAAS,GAAAtB,OAAA,CAAAsB,SAAA,GAAiD7C,IAAI,CAAC8C,aAAa;AAEzF;;;;AAIO,MAAMC,OAAO,GAAAxB,OAAA,CAAAwB,OAAA,GAWhB/C,IAAI,CAACgD,WAAW;AAEpB;;;;AAIO,MAAMC,aAAa,GAAA1B,OAAA,CAAA0B,aAAA,GAWtBjD,IAAI,CAACkD,iBAAiB;AAE1B;;;;AAIO,MAAMC,OAAO,GAAA5B,OAAA,CAAA4B,OAAA,GAA8DnD,IAAI,CAACoD,WAAW;AAElG;;;;AAIO,MAAMC,aAAa,GAAA9B,OAAA,CAAA8B,aAAA,GAWtBrD,IAAI,CAACsD,iBAAiB;AAE1B;;;;;;AAMO,MAAMC,UAAU,GAAAhC,OAAA,CAAAgC,UAAA,GAAsDvD,IAAI,CAACwD,cAAc;AAEhG;;;;;;AAMO,MAAMC,UAAU,GAAAlC,OAAA,CAAAkC,UAAA,GAAmDzD,IAAI,CAAC0D,cAAc;AAE7F;;;;;;;;AAQO,MAAMC,SAAS,GAAApC,OAAA,CAAAoC,SAAA,GAmBlB3D,IAAI,CAAC4D,aAAa;AAEtB;;;;;;;AAOO,MAAMC,SAAS,GAAAtC,OAAA,CAAAsC,SAAA,GAA8C7D,IAAI,CAAC8D,aAAa;AAEtF;;;;;;;AAOO,MAAMC,GAAG,GAAAxC,OAAA,CAAAwC,GAAA,GAiBZ/D,IAAI,CAACgE,OAAO;AAEhB;;;;;;;AAOO,MAAMC,OAAO,GAAA1C,OAAA,CAAA0C,OAAA,GAsBhBjE,IAAI,CAACkE,WAAW;AAEpB;;;;;;;AAOO,MAAMC,QAAQ,GAAA5C,OAAA,CAAA4C,QAAA,GAiBjBnE,IAAI,CAACoE,YAAY;AAErB;;;;;;;AAOO,MAAMC,aAAa,GAAA9C,OAAA,CAAA8C,aAAA,GAiBtBrE,IAAI,CAACsE,iBAAiB;AAE1B;;;;AAIO,MAAMC,KAAK,GAAAhD,OAAA,CAAAgD,KAAA,GAgBdvE,IAAI,CAACwE,SAAS;AAElB;;;;AAIO,MAAMC,WAAW,GAAAlD,OAAA,CAAAkD,WAAA,GAsBpBzE,IAAI,CAAC0E,eAAe;AAExB;;;;;;AAMO,MAAMC,OAAO,GAAApD,OAAA,CAAAoD,OAAA,GAA6B3E,IAAI,CAAC4E,WAAW;AAEjE,MAAMC,KAAK,GAAAtD,OAAA,CAAAuD,IAAA,GAAe9E,IAAI,CAAC+E,QAAQ;AAWvC;;;;;;;AAOO,MAAMC,GAAG,GAAAzD,OAAA,CAAAyD,GAAA,GAiBZhF,IAAI,CAACiF,OAAO;AAEhB;;;;;;;AAOO,MAAMC,OAAO,GAAA3D,OAAA,CAAA2D,OAAA,GAiBhBlF,IAAI,CAACmF,WAAW;AAEpB;;;;;;;AAOO,MAAMC,QAAQ,GAAA7D,OAAA,CAAA6D,QAAA,GAiBjBpF,IAAI,CAACqF,YAAY;AAErB;;;;;;;AAOO,MAAMC,MAAM,GAAA/D,OAAA,CAAA+D,MAAA,GAiBftF,IAAI,CAACuF,UAAU;AAEnB;;;;;;;AAOO,MAAMC,UAAU,GAAAjE,OAAA,CAAAiE,UAAA,GAiBnBxF,IAAI,CAACyF,cAAc;AAEvB;;;;;;;AAOO,MAAMC,WAAW,GAAAnE,OAAA,CAAAmE,WAAA,GAiBpB1F,IAAI,CAAC2F,eAAe;AAExB;;;;;;;AAOO,MAAMC,OAAO,GAAArE,OAAA,CAAAqE,OAAA,GA8BhB5F,IAAI,CAAC6F,WAAW", "ignoreList": []}