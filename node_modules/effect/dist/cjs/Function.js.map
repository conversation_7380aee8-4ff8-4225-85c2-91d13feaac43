{"version": 3, "file": "Function.js", "names": ["isFunction", "input", "exports", "dual", "arity", "body", "arguments", "apply", "self", "RangeError", "a", "b", "length", "c", "d", "e", "args", "identity", "satisfies", "unsafeCoerce", "constant", "value", "constTrue", "constFalse", "const<PERSON><PERSON>", "constUndefined", "undefined", "constVoid", "flip", "f", "compose", "ab", "bc", "absurd", "_", "Error", "tupled", "untupled", "pipe", "cd", "de", "ef", "fg", "gh", "hi", "ret", "i", "flow", "ij", "hole", "SK"], "sources": ["../../src/Function.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;AAaA;;;;;;;;;;;;;;;AAeO,MAAMA,UAAU,GAAIC,KAAc,IAAwB,OAAOA,KAAK,KAAK,UAAU;AAE5F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAAC,OAAA,CAAAF,UAAA,GAAAA,UAAA;AAgEO,MAAMG,IAAI,GAmIb,SAAAA,CAASC,KAAK,EAAEC,IAAI;EACtB,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAO;MACL,IAAIA,KAAK,CAACE,SAAS,CAAC,EAAE;QACpB;QACA,OAAOD,IAAI,CAACE,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;MACpC;MACA,OAASE,IAAS,IAAKH,IAAI,CAACG,IAAI,EAAE,GAAGF,SAAS,CAAC;IACjD,CAAC;EACH;EAEA,QAAQF,KAAK;IACX,KAAK,CAAC;IACN,KAAK,CAAC;MACJ,MAAM,IAAIK,UAAU,CAAC,iBAAiBL,KAAK,EAAE,CAAC;IAEhD,KAAK,CAAC;MACJ,OAAO,UAASM,CAAC,EAAEC,CAAC;QAClB,IAAIL,SAAS,CAACM,MAAM,IAAI,CAAC,EAAE;UACzB,OAAOP,IAAI,CAACK,CAAC,EAAEC,CAAC,CAAC;QACnB;QACA,OAAO,UAASH,IAAS;UACvB,OAAOH,IAAI,CAACG,IAAI,EAAEE,CAAC,CAAC;QACtB,CAAC;MACH,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAASA,CAAC,EAAEC,CAAC,EAAEE,CAAC;QACrB,IAAIP,SAAS,CAACM,MAAM,IAAI,CAAC,EAAE;UACzB,OAAOP,IAAI,CAACK,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC;QACtB;QACA,OAAO,UAASL,IAAS;UACvB,OAAOH,IAAI,CAACG,IAAI,EAAEE,CAAC,EAAEC,CAAC,CAAC;QACzB,CAAC;MACH,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAASD,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC;QACxB,IAAIR,SAAS,CAACM,MAAM,IAAI,CAAC,EAAE;UACzB,OAAOP,IAAI,CAACK,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC;QACzB;QACA,OAAO,UAASN,IAAS;UACvB,OAAOH,IAAI,CAACG,IAAI,EAAEE,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAC;QAC5B,CAAC;MACH,CAAC;IAEH,KAAK,CAAC;MACJ,OAAO,UAASH,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC;QAC3B,IAAIT,SAAS,CAACM,MAAM,IAAI,CAAC,EAAE;UACzB,OAAOP,IAAI,CAACK,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;QAC5B;QACA,OAAO,UAASP,IAAS;UACvB,OAAOH,IAAI,CAACG,IAAI,EAAEE,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC;QAC/B,CAAC;MACH,CAAC;IAEH;MACE,OAAO;QACL,IAAIR,SAAS,CAACM,MAAM,IAAIR,KAAK,EAAE;UAC7B;UACA,OAAOC,IAAI,CAACE,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;QACpC;QACA,MAAMU,IAAI,GAAGV,SAAS;QACtB,OAAO,UAASE,IAAS;UACvB,OAAOH,IAAI,CAACG,IAAI,EAAE,GAAGQ,IAAI,CAAC;QAC5B,CAAC;MACH,CAAC;EACL;AACF,CAAC;AACD;;;;;;;;;;;;;;AAAAd,OAAA,CAAAC,IAAA,GAAAA,IAAA;AAcO,MAAMI,KAAK,GAAGA,CAAmC,GAAGG,CAAI,KAASF,IAAoB,IAAQA,IAAI,CAAC,GAAGE,CAAC,CAAC;AAkC9G;;;;;;;;;;;;;AAAAR,OAAA,CAAAK,KAAA,GAAAA,KAAA;AAaO,MAAMU,QAAQ,GAAOP,CAAI,IAAQA,CAAC;AAEzC;;;;;;;;;;;;;;;;;;;;AAAAR,OAAA,CAAAe,QAAA,GAAAA,QAAA;AAoBO,MAAMC,SAAS,GAAGA,CAAA,KAAuBP,CAAI,IAAKA,CAAC;AAE1D;;;;;;;;;;;;;AAAAT,OAAA,CAAAgB,SAAA,GAAAA,SAAA;AAaO,MAAMC,YAAY,GAAAjB,OAAA,CAAAiB,YAAA,GAAsBF,QAAe;AAE9D;;;;;;;;;;;;;;;;;;;AAmBO,MAAMG,QAAQ,GAAOC,KAAQ,IAAiB,MAAMA,KAAK;AAEhE;;;;;;;;;;;;;AAAAnB,OAAA,CAAAkB,QAAA,GAAAA,QAAA;AAaO,MAAME,SAAS,GAAApB,OAAA,CAAAoB,SAAA,gBAAqBF,QAAQ,CAAC,IAAI,CAAC;AAEzD;;;;;;;;;;;;;AAaO,MAAMG,UAAU,GAAArB,OAAA,CAAAqB,UAAA,gBAAqBH,QAAQ,CAAC,KAAK,CAAC;AAE3D;;;;;;;;;;;;;AAaO,MAAMI,SAAS,GAAAtB,OAAA,CAAAsB,SAAA,gBAAkBJ,QAAQ,CAAC,IAAI,CAAC;AAEtD;;;;;;;;;;;;;AAaO,MAAMK,cAAc,GAAAvB,OAAA,CAAAuB,cAAA,gBAAuBL,QAAQ,CAACM,SAAS,CAAC;AAErE;;;;;;;;;;;;;AAaO,MAAMC,SAAS,GAAAzB,OAAA,CAAAyB,SAAA,GAAkBF,cAAc;AAEtD;;;;;;;;;;;;;;;AAeO,MAAMG,IAAI,GACfC,CAA8B,IAEhC,CAAC,GAAGlB,CAAC,KACL,CAAC,GAAGD,CAAC,KAAKmB,CAAC,CAAC,GAAGnB,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC;AAEvB;;;;;;;;;;;;;;;;;AAAAT,OAAA,CAAA0B,IAAA,GAAAA,IAAA;AAiBO,MAAME,OAAO,GAAA5B,OAAA,CAAA4B,OAAA,gBAqChB3B,IAAI,CAAC,CAAC,EAAE,CAAU4B,EAAe,EAAEC,EAAe,KAAmBtB,CAAC,IAAKsB,EAAE,CAACD,EAAE,CAACrB,CAAC,CAAC,CAAC,CAAC;AAEzF;;;;;;;;AAQO,MAAMuB,MAAM,GAAOC,CAAQ,IAAO;EACvC,MAAM,IAAIC,KAAK,CAAC,qDAAqD,CAAC;AACxE,CAAC;AAED;;;;;;;;;;;;;;;AAAAjC,OAAA,CAAA+B,MAAA,GAAAA,MAAA;AAeO,MAAMG,MAAM,GAAyCP,CAAiB,IAAmBnB,CAAC,IAAKmB,CAAC,CAAC,GAAGnB,CAAC,CAAC;AAE7G;;;;;;;;;;;;;;;AAAAR,OAAA,CAAAkC,MAAA,GAAAA,MAAA;AAeO,MAAMC,QAAQ,GAAyCR,CAAc,IAAqB,CAAC,GAAGnB,CAAC,KAAKmB,CAAC,CAACnB,CAAC,CAAC;AAAAR,OAAA,CAAAmC,QAAA,GAAAA,QAAA;AAsgBzG,SAAUC,IAAIA,CAClB5B,CAAU,EACVqB,EAAa,EACbC,EAAa,EACbO,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa;EAEb,QAAQtC,SAAS,CAACM,MAAM;IACtB,KAAK,CAAC;MACJ,OAAOF,CAAC;IACV,KAAK,CAAC;MACJ,OAAOqB,EAAG,CAACrB,CAAC,CAAC;IACf,KAAK,CAAC;MACJ,OAAOsB,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC;IACpB,KAAK,CAAC;MACJ,OAAO6B,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC;IACzB,KAAK,CAAC;MACJ,OAAO8B,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,KAAK,CAAC;MACJ,OAAO+B,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC,KAAK,CAAC;MACJ,OAAOgC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,KAAK,CAAC;MACJ,OAAOiC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,KAAK,CAAC;MACJ,OAAOkC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAG,CAACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAClD;MAAS;QACP,IAAImC,GAAG,GAAGvC,SAAS,CAAC,CAAC,CAAC;QACtB,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxC,SAAS,CAACM,MAAM,EAAEkC,CAAC,EAAE,EAAE;UACzCD,GAAG,GAAGvC,SAAS,CAACwC,CAAC,CAAC,CAACD,GAAG,CAAC;QACzB;QACA,OAAOA,GAAG;MACZ;EACF;AACF;AAyIM,SAAUE,IAAIA,CAClBhB,EAAY,EACZC,EAAa,EACbO,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbC,EAAa,EACbI,EAAa;EAEb,QAAQ1C,SAAS,CAACM,MAAM;IACtB,KAAK,CAAC;MACJ,OAAOmB,EAAE;IACX,KAAK,CAAC;MACJ,OAAO;QACL,OAAOC,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC;MACvC,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOiC,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC;MAC5C,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOkC,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOmC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACtD,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOoC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOqC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAOsC,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrE,CAAC;IACH,KAAK,CAAC;MACJ,OAAO;QACL,OAAO0C,EAAG,CAACJ,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACD,EAAG,CAACP,EAAG,CAACD,EAAE,CAACxB,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1E,CAAC;EACL;EACA;AACF;AAEA;;;;;AAKO,MAAM2C,IAAI,GAAA/C,OAAA,CAAA+C,IAAA,gBAAe9B,YAAY,CAACc,MAAM,CAAC;AAEpD;;;;;;;;;;;;;;;;AAgBO,MAAMiB,EAAE,GAAGA,CAAOhB,CAAI,EAAEvB,CAAI,KAAQA,CAAC;AAAAT,OAAA,CAAAgD,EAAA,GAAAA,EAAA", "ignoreList": []}