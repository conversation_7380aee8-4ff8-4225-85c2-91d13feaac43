"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.summary = exports.isSummaryKey = exports.isMetricKeyType = exports.isHistogramKey = exports.isGaugeKey = exports.isFrequencyKey = exports.isCounterKey = exports.histogram = exports.gauge = exports.frequency = exports.counter = exports.SummaryKeyTypeTypeId = exports.MetricKeyTypeTypeId = exports.HistogramKeyTypeTypeId = exports.GaugeKeyTypeTypeId = exports.FrequencyKeyTypeTypeId = exports.CounterKeyTypeTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/metric/keyType.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const MetricKeyTypeTypeId = exports.MetricKeyTypeTypeId = internal.MetricKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
const CounterKeyTypeTypeId = exports.CounterKeyTypeTypeId = internal.CounterKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
const FrequencyKeyTypeTypeId = exports.FrequencyKeyTypeTypeId = internal.FrequencyKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
const GaugeKeyTypeTypeId = exports.GaugeKeyTypeTypeId = internal.GaugeKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
const HistogramKeyTypeTypeId = exports.HistogramKeyTypeTypeId = internal.HistogramKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
const SummaryKeyTypeTypeId = exports.SummaryKeyTypeTypeId = internal.SummaryKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const counter = exports.counter = internal.counter;
/**
 * @since 2.0.0
 * @category constructors
 */
const frequency = exports.frequency = internal.frequency;
/**
 * @since 2.0.0
 * @category constructors
 */
const gauge = exports.gauge = internal.gauge;
/**
 * @since 2.0.0
 * @category constructors
 */
const histogram = exports.histogram = internal.histogram;
/**
 * @since 2.0.0
 * @category constructors
 */
const summary = exports.summary = internal.summary;
/**
 * @since 2.0.0
 * @category refinements
 */
const isMetricKeyType = exports.isMetricKeyType = internal.isMetricKeyType;
/**
 * @since 2.0.0
 * @category refinements
 */
const isCounterKey = exports.isCounterKey = internal.isCounterKey;
/**
 * @since 2.0.0
 * @category refinements
 */
const isFrequencyKey = exports.isFrequencyKey = internal.isFrequencyKey;
/**
 * @since 2.0.0
 * @category refinements
 */
const isGaugeKey = exports.isGaugeKey = internal.isGaugeKey;
/**
 * @since 2.0.0
 * @category refinements
 */
const isHistogramKey = exports.isHistogramKey = internal.isHistogramKey;
/**
 * @since 2.0.0
 * @category refinements
 */
const isSummaryKey = exports.isSummaryKey = internal.isSummaryKey;
//# sourceMappingURL=MetricKeyType.js.map