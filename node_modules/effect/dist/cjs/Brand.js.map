{"version": 3, "file": "Brand.js", "names": ["Arr", "_interopRequireWildcard", "require", "Either", "_Function", "Option", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "BrandTypeId", "exports", "Symbol", "for", "RefinedConstructorsTypeId", "error", "message", "meta", "errors", "flatten", "refined", "args", "either", "length", "unbranded", "right", "left", "match", "onNone", "onSome", "assign", "getOrThrowWith", "identity", "option", "getRight", "is", "isRight", "nominal", "some", "_args", "all", "brands", "result", "brand", "nextResult", "isLeft", "onLeft", "onRight", "unsafeCoerce"], "sources": ["../../src/Brand.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;AAkBA,IAAAA,GAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAqC,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AArBrC;;;;;;;;;;;;;;;;;;;AAyBA;;;;AAIO,MAAMkB,WAAW,GAAAC,OAAA,CAAAD,WAAA,gBAAkBE,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;AAQpE;;;;AAIO,MAAMC,yBAAyB,GAAAH,OAAA,CAAAG,yBAAA,gBAAkBF,MAAM,CAACC,GAAG,CAAC,sBAAsB,CAAC;AA6H1F;;;;;;AAMO,MAAME,KAAK,GAAGA,CAACC,OAAe,EAAEC,IAAc,KAAwB,CAAC;EAC5ED,OAAO;EACPC;CACD,CAAC;AAEF;;;;;;AAAAN,OAAA,CAAAI,KAAA,GAAAA,KAAA;AAMO,MAAMG,MAAM,GAA+DA,CAChF,GAAGA,MAAgC,KACbjC,GAAG,CAACkC,OAAO,CAACD,MAAM,CAAC;AAAAP,OAAA,CAAAO,MAAA,GAAAA,MAAA;AAsCrC,SAAUE,OAAOA,CACrB,GAAGC,IAGF;EAED,MAAMC,MAAM,GAA2ED,IAAI,CAACE,MAAM,KAAK,CAAC,GACrGC,SAAS,IAAKH,IAAI,CAAC,CAAC,CAAC,CAACG,SAAS,CAAC,GAAGpC,MAAM,CAACqC,KAAK,CAACD,SAAc,CAAC,GAAGpC,MAAM,CAACsC,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,GACjGA,SAAS,IAAI;IACZ,OAAOlC,MAAM,CAACqC,KAAK,CAACN,IAAI,CAAC,CAAC,CAAC,CAACG,SAAS,CAAC,EAAE;MACtCI,MAAM,EAAEA,CAAA,KAAMxC,MAAM,CAACqC,KAAK,CAACD,SAAc,CAAC;MAC1CK,MAAM,EAAEzC,MAAM,CAACsC;KAChB,CAAC;EACJ,CAAC;EACH,OAAOnB,MAAM,CAACuB,MAAM,CAAEN,SAA6B,IAAKpC,MAAM,CAAC2C,cAAc,CAACT,MAAM,CAACE,SAAS,CAAC,EAAEQ,kBAAQ,CAAC,EAAE;IAC1G,CAAClB,yBAAyB,GAAGA,yBAAyB;IACtDmB,MAAM,EAAGZ,IAAS,IAAK/B,MAAM,CAAC4C,QAAQ,CAACZ,MAAM,CAACD,IAAI,CAAC,CAAC;IACpDC,MAAM;IACNa,EAAE,EAAGd,IAAS,IAAqCjC,MAAM,CAACgD,OAAO,CAACd,MAAM,CAACD,IAAI,CAAC;GAC/E,CAAQ;AACX;AAEA;;;;;;;;;;;;;;;;;;;;;;;AAuBO,MAAMgB,OAAO,GAAGA,CAAA,KAEnB;EACF;EACA,OAAO9B,MAAM,CAACuB,MAAM,CAAET,IAAI,IAAKA,IAAI,EAAE;IACnC,CAACP,yBAAyB,GAAGA,yBAAyB;IACtDmB,MAAM,EAAGZ,IAAS,IAAK/B,MAAM,CAACgD,IAAI,CAACjB,IAAI,CAAC;IACxCC,MAAM,EAAGD,IAAS,IAAKjC,MAAM,CAACqC,KAAK,CAACJ,IAAI,CAAC;IACzCc,EAAE,EAAGI,KAAU,IAAsC;GACtD,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA5B,OAAA,CAAA0B,OAAA,GAAAA,OAAA;AAgCO,MAAMG,GAAG,GAKZA,CAEF,GAAGC,MAAsC,KAMvC;EACF,MAAMnB,MAAM,GAAID,IAAS,IAA2C;IAClE,IAAIqB,MAAM,GAA0CtD,MAAM,CAACqC,KAAK,CAACJ,IAAI,CAAC;IACtE,KAAK,MAAMsB,KAAK,IAAIF,MAAM,EAAE;MAC1B,MAAMG,UAAU,GAAGD,KAAK,CAACrB,MAAM,CAACD,IAAI,CAAC;MACrC,IAAIjC,MAAM,CAACyD,MAAM,CAACH,MAAM,CAAC,IAAItD,MAAM,CAACyD,MAAM,CAACD,UAAU,CAAC,EAAE;QACtDF,MAAM,GAAGtD,MAAM,CAACsC,IAAI,CAAC,CAAC,GAAGgB,MAAM,CAAChB,IAAI,EAAE,GAAGkB,UAAU,CAAClB,IAAI,CAAC,CAAC;MAC5D,CAAC,MAAM;QACLgB,MAAM,GAAGtD,MAAM,CAACyD,MAAM,CAACH,MAAM,CAAC,GAAGA,MAAM,GAAGE,UAAU;MACtD;IACF;IACA,OAAOF,MAAM;EACf,CAAC;EACD;EACA,OAAOnC,MAAM,CAACuB,MAAM,CAAET,IAAI,IACxBjC,MAAM,CAACuC,KAAK,CAACL,MAAM,CAACD,IAAI,CAAC,EAAE;IACzByB,MAAM,EAAGvD,CAAC,IAAI;MACZ,MAAMA,CAAC;IACT,CAAC;IACDwD,OAAO,EAAEf;GACV,CAAC,EAAE;IACJ,CAAClB,yBAAyB,GAAGA,yBAAyB;IACtDmB,MAAM,EAAGZ,IAAS,IAAK/B,MAAM,CAAC4C,QAAQ,CAACZ,MAAM,CAACD,IAAI,CAAC,CAAC;IACpDC,MAAM;IACNa,EAAE,EAAGd,IAAS,IAAkBjC,MAAM,CAACgD,OAAO,CAACd,MAAM,CAACD,IAAI,CAAC;GAC5D,CAAC;AACJ,CAAC;AAED;;;;;;AAAAV,OAAA,CAAA6B,GAAA,GAAAA,GAAA;AAMO,MAAMhB,SAAS,GAAAb,OAAA,CAAAa,SAAA,GAA6DwB,sBAAY", "ignoreList": []}