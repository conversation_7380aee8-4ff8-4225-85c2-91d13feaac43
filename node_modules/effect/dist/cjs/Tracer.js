"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.tracerWith = exports.make = exports.externalSpan = exports.TracerTypeId = exports.Tracer = exports.ParentSpan = exports.DisablePropagation = void 0;
var defaultServices = _interopRequireWildcard(require("./internal/defaultServices.js"));
var internal = _interopRequireWildcard(require("./internal/tracer.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 */
const TracerTypeId = exports.TracerTypeId = internal.TracerTypeId;
/**
 * @since 2.0.0
 * @category tags
 */
const ParentSpan = exports.ParentSpan = internal.spanTag;
/**
 * @since 2.0.0
 * @category tags
 */
const Tracer = exports.Tracer = internal.tracerTag;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
const externalSpan = exports.externalSpan = internal.externalSpan;
/**
 * @since 2.0.0
 * @category constructors
 */
const tracerWith = exports.tracerWith = defaultServices.tracerWith;
/**
 * @since 3.12.0
 * @category annotations
 */
const DisablePropagation = exports.DisablePropagation = internal.DisablePropagation;
//# sourceMappingURL=Tracer.js.map