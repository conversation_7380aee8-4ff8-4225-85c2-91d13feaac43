"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.linear = exports.isMetricBoundaries = exports.fromIterable = exports.exponential = exports.MetricBoundariesTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/metric/boundaries.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const MetricBoundariesTypeId = exports.MetricBoundariesTypeId = internal.MetricBoundariesTypeId;
/**
 * @since 2.0.0
 * @category refinements
 */
const isMetricBoundaries = exports.isMetricBoundaries = internal.isMetricBoundaries;
/**
 * @since 2.0.0
 * @category constructors
 */
const fromIterable = exports.fromIterable = internal.fromIterable;
/**
 * A helper method to create histogram bucket boundaries for a histogram
 * with linear increasing values.
 *
 * @since 2.0.0
 * @category constructors
 */
const linear = exports.linear = internal.linear;
/**
 * A helper method to create histogram bucket boundaries for a histogram
 * with exponentially increasing values.
 *
 * @since 2.0.0
 * @category constructors
 */
const exponential = exports.exponential = internal.exponential;
//# sourceMappingURL=MetricBoundaries.js.map