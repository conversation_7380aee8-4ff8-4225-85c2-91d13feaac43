"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.summary = exports.onUpdate = exports.onModify = exports.make = exports.histogram = exports.gauge = exports.frequency = exports.counter = exports.MetricHookTypeId = void 0;
var internal = _interopRequireWildcard(require("./internal/metric/hook.js"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
/**
 * @since 2.0.0
 * @category symbols
 */
const MetricHookTypeId = exports.MetricHookTypeId = internal.MetricHookTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
const make = exports.make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
const counter = exports.counter = internal.counter;
/**
 * @since 2.0.0
 * @category constructors
 */
const frequency = exports.frequency = internal.frequency;
/**
 * @since 2.0.0
 * @category constructors
 */
const gauge = exports.gauge = internal.gauge;
/**
 * @since 2.0.0
 * @category constructors
 */
const histogram = exports.histogram = internal.histogram;
/**
 * @since 2.0.0
 * @category constructors
 */
const summary = exports.summary = internal.summary;
/**
 * @since 2.0.0
 * @category utils
 */
const onUpdate = exports.onUpdate = internal.onUpdate;
/**
 * @since 3.6.5
 * @category utils
 */
const onModify = exports.onModify = internal.onModify;
//# sourceMappingURL=MetricHook.js.map