{"version": 3, "file": "Deferred.js", "names": ["core", "_interopRequireWildcard", "require", "internal", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "DeferredTypeId", "exports", "make", "deferred<PERSON><PERSON>", "makeAs", "deferred<PERSON>akeAs", "_await", "await", "deferred<PERSON><PERSON><PERSON>", "complete", "deferredComplete", "completeWith", "deferredCompleteWith", "done", "deferredDone", "fail", "deferredFail", "failSync", "deferredFailSync", "failCause", "deferred<PERSON>ail<PERSON><PERSON><PERSON>", "failCauseSync", "deferredFailCauseSync", "die", "deferred<PERSON>ie", "dieSync", "deferred<PERSON><PERSON><PERSON><PERSON>", "interrupt", "deferredInterrupt", "interruptWith", "deferredInterruptWith", "isDone", "deferredIsDone", "poll", "deferred<PERSON><PERSON>", "succeed", "deferred<PERSON>ucceed", "sync", "deferredSync", "unsafeMake", "deferredUnsafeMake", "unsafeDone", "deferredUnsafeDone"], "sources": ["../../src/Deferred.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAQA,IAAAA,IAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAkD,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAMlD;;;;AAIO,MAAMkB,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAkBpB,QAAQ,CAACoB,cAAc;AA8DpE;;;;;;AAMO,MAAME,IAAI,GAAAD,OAAA,CAAAC,IAAA,GAAsDzB,IAAI,CAAC0B,YAAY;AAExF;;;;;;AAMO,MAAMC,MAAM,GAAAH,OAAA,CAAAG,MAAA,GAA8E3B,IAAI,CAAC4B,cAAc;AAEpH,MAAMC,MAAM,GAAAL,OAAA,CAAAM,KAAA,GAAwD9B,IAAI,CAAC+B,aAAa;AAatF;;;;;;;;;;AAUO,MAAMC,QAAQ,GAAAR,OAAA,CAAAQ,QAAA,GAuBjBhC,IAAI,CAACiC,gBAAgB;AAEzB;;;;;;;AAOO,MAAMC,YAAY,GAAAV,OAAA,CAAAU,YAAA,GAiBrBlC,IAAI,CAACmC,oBAAoB;AAE7B;;;;;;;AAOO,MAAMC,IAAI,GAAAZ,OAAA,CAAAY,IAAA,GAiBbpC,IAAI,CAACqC,YAAY;AAErB;;;;;;;AAOO,MAAMC,IAAI,GAAAd,OAAA,CAAAc,IAAA,GAiBbtC,IAAI,CAACuC,YAAY;AAErB;;;;;;;AAOO,MAAMC,QAAQ,GAAAhB,OAAA,CAAAgB,QAAA,GAiBjBxC,IAAI,CAACyC,gBAAgB;AAEzB;;;;;;;AAOO,MAAMC,SAAS,GAAAlB,OAAA,CAAAkB,SAAA,GAiBlB1C,IAAI,CAAC2C,iBAAiB;AAE1B;;;;;;;AAOO,MAAMC,aAAa,GAAApB,OAAA,CAAAoB,aAAA,GAiBtB5C,IAAI,CAAC6C,qBAAqB;AAE9B;;;;;;;AAOO,MAAMC,GAAG,GAAAtB,OAAA,CAAAsB,GAAA,GAiBZ9C,IAAI,CAAC+C,WAAW;AAEpB;;;;;;;AAOO,MAAMC,OAAO,GAAAxB,OAAA,CAAAwB,OAAA,GAiBhBhD,IAAI,CAACiD,eAAe;AAExB;;;;;;;;AAQO,MAAMC,SAAS,GAAA1B,OAAA,CAAA0B,SAAA,GAA2DlD,IAAI,CAACmD,iBAAiB;AAEvG;;;;;;;AAOO,MAAMC,aAAa,GAAA5B,OAAA,CAAA4B,aAAA,GAiBtBpD,IAAI,CAACqD,qBAAqB;AAE9B;;;;;;;AAOO,MAAMC,MAAM,GAAA9B,OAAA,CAAA8B,MAAA,GAA2DtD,IAAI,CAACuD,cAAc;AAEjG;;;;;;;AAOO,MAAMC,IAAI,GAAAhC,OAAA,CAAAgC,IAAA,GAEwCxD,IAAI,CAACyD,YAAY;AAE1E;;;;;;AAMO,MAAMC,OAAO,GAAAlC,OAAA,CAAAkC,OAAA,GAehB1D,IAAI,CAAC2D,eAAe;AAExB;;;;;;AAMO,MAAMC,IAAI,GAAApC,OAAA,CAAAoC,IAAA,GAeb5D,IAAI,CAAC6D,YAAY;AAErB;;;;;;AAMO,MAAMC,UAAU,GAAAtC,OAAA,CAAAsC,UAAA,GAA+D9D,IAAI,CAAC+D,kBAAkB;AAE7G;;;;;;;AAOO,MAAMC,UAAU,GAAAxC,OAAA,CAAAwC,UAAA,GAAsEhE,IAAI,CAACiE,kBAAkB", "ignoreList": []}