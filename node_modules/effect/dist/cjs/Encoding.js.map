{"version": 3, "file": "Encoding.js", "names": ["Either", "_interopRequireWildcard", "require", "Base64", "Base64Url", "Common", "Hex", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "encodeBase64", "input", "encode", "encoder", "exports", "decodeBase64", "str", "decode", "decodeBase64String", "map", "_", "decoder", "encodeBase64Url", "decodeBase64Url", "decodeBase64UrlString", "encodeHex", "decodeHex", "decodeHexString", "encodeUriComponent", "try", "encodeURIComponent", "catch", "EncodeException", "Error", "message", "decodeUriComponent", "decodeURIComponent", "DecodeException", "DecodeExceptionTypeId", "isDecodeException", "EncodeExceptionTypeId", "isEncodeException"], "sources": ["../../src/Encoding.ts"], "sourcesContent": [null], "mappings": ";;;;;;AASA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAF,uBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAH,uBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,uBAAA,CAAAC,OAAA;AACA,IAAAI,GAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAiD,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAbjD;;;;;;;;;;AAeA;;;;;;AAMO,MAAMkB,YAAY,GAA4CC,KAAK,IACxE,OAAOA,KAAK,KAAK,QAAQ,GAAGxB,MAAM,CAACyB,MAAM,CAACvB,MAAM,CAACwB,OAAO,CAACD,MAAM,CAACD,KAAK,CAAC,CAAC,GAAGxB,MAAM,CAACyB,MAAM,CAACD,KAAK,CAAC;AAEhG;;;;;;AAAAG,OAAA,CAAAJ,YAAA,GAAAA,YAAA;AAMO,MAAMK,YAAY,GAAIC,GAAW,IAAiD7B,MAAM,CAAC8B,MAAM,CAACD,GAAG,CAAC;AAE3G;;;;;;AAAAF,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAMO,MAAMG,kBAAkB,GAAIF,GAAW,IAAKhC,MAAM,CAACmC,GAAG,CAACJ,YAAY,CAACC,GAAG,CAAC,EAAGI,CAAC,IAAK/B,MAAM,CAACgC,OAAO,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC;AAEjH;;;;;;AAAAN,OAAA,CAAAI,kBAAA,GAAAA,kBAAA;AAMO,MAAMI,eAAe,GAA4CX,KAAK,IAC3E,OAAOA,KAAK,KAAK,QAAQ,GAAGvB,SAAS,CAACwB,MAAM,CAACvB,MAAM,CAACwB,OAAO,CAACD,MAAM,CAACD,KAAK,CAAC,CAAC,GAAGvB,SAAS,CAACwB,MAAM,CAACD,KAAK,CAAC;AAEtG;;;;;;AAAAG,OAAA,CAAAQ,eAAA,GAAAA,eAAA;AAMO,MAAMC,eAAe,GAAIP,GAAW,IAAiD5B,SAAS,CAAC6B,MAAM,CAACD,GAAG,CAAC;AAEjH;;;;;;AAAAF,OAAA,CAAAS,eAAA,GAAAA,eAAA;AAMO,MAAMC,qBAAqB,GAAIR,GAAW,IAAKhC,MAAM,CAACmC,GAAG,CAACI,eAAe,CAACP,GAAG,CAAC,EAAGI,CAAC,IAAK/B,MAAM,CAACgC,OAAO,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC;AAEvH;;;;;;AAAAN,OAAA,CAAAU,qBAAA,GAAAA,qBAAA;AAMO,MAAMC,SAAS,GAA4Cd,KAAK,IACrE,OAAOA,KAAK,KAAK,QAAQ,GAAGrB,GAAG,CAACsB,MAAM,CAACvB,MAAM,CAACwB,OAAO,CAACD,MAAM,CAACD,KAAK,CAAC,CAAC,GAAGrB,GAAG,CAACsB,MAAM,CAACD,KAAK,CAAC;AAE1F;;;;;;AAAAG,OAAA,CAAAW,SAAA,GAAAA,SAAA;AAMO,MAAMC,SAAS,GAAIV,GAAW,IAAiD1B,GAAG,CAAC2B,MAAM,CAACD,GAAG,CAAC;AAErG;;;;;;AAAAF,OAAA,CAAAY,SAAA,GAAAA,SAAA;AAMO,MAAMC,eAAe,GAAIX,GAAW,IAAKhC,MAAM,CAACmC,GAAG,CAACO,SAAS,CAACV,GAAG,CAAC,EAAGI,CAAC,IAAK/B,MAAM,CAACgC,OAAO,CAACJ,MAAM,CAACG,CAAC,CAAC,CAAC;AAE3G;;;;;;AAAAN,OAAA,CAAAa,eAAA,GAAAA,eAAA;AAMO,MAAMC,kBAAkB,GAAIZ,GAAW,IAC5ChC,MAAM,CAAC6C,GAAG,CAAC;EACTA,GAAG,EAAEA,CAAA,KAAMC,kBAAkB,CAACd,GAAG,CAAC;EAClCe,KAAK,EAAGxC,CAAC,IAAKyC,eAAe,CAAChB,GAAG,EAAEzB,CAAC,YAAY0C,KAAK,GAAG1C,CAAC,CAAC2C,OAAO,GAAG,eAAe;CACpF,CAAC;AAEJ;;;;;;AAAApB,OAAA,CAAAc,kBAAA,GAAAA,kBAAA;AAMO,MAAMO,kBAAkB,GAAInB,GAAW,IAC5ChC,MAAM,CAAC6C,GAAG,CAAC;EACTA,GAAG,EAAEA,CAAA,KAAMO,kBAAkB,CAACpB,GAAG,CAAC;EAClCe,KAAK,EAAGxC,CAAC,IAAK8C,eAAe,CAACrB,GAAG,EAAEzB,CAAC,YAAY0C,KAAK,GAAG1C,CAAC,CAAC2C,OAAO,GAAG,eAAe;CACpF,CAAC;AAEJ;;;;AAAApB,OAAA,CAAAqB,kBAAA,GAAAA,kBAAA;AAIO,MAAMG,qBAAqB,GAAAxB,OAAA,CAAAwB,qBAAA,GAAkBjD,MAAM,CAACiD,qBAAqB;AAqBhF;;;;;;AAMO,MAAMD,eAAe,GAAAvB,OAAA,CAAAuB,eAAA,GAAyDhD,MAAM,CAACgD,eAAe;AAE3G;;;;;;AAMO,MAAME,iBAAiB,GAAAzB,OAAA,CAAAyB,iBAAA,GAAyClD,MAAM,CAACkD,iBAAiB;AAE/F;;;;AAIO,MAAMC,qBAAqB,GAAA1B,OAAA,CAAA0B,qBAAA,GAAkBnD,MAAM,CAACmD,qBAAqB;AAqBhF;;;;;;AAMO,MAAMR,eAAe,GAAAlB,OAAA,CAAAkB,eAAA,GAAyD3C,MAAM,CAAC2C,eAAe;AAE3G;;;;;;AAMO,MAAMS,iBAAiB,GAAA3B,OAAA,CAAA2B,iBAAA,GAAyCpD,MAAM,CAACoD,iBAAiB", "ignoreList": []}