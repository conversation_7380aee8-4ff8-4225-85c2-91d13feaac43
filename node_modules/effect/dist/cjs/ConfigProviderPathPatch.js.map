{"version": 3, "file": "ConfigProviderPathPatch.js", "names": ["internal", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "empty", "exports", "and<PERSON><PERSON>", "mapName", "nested", "unnested"], "sources": ["../../src/ConfigProviderPathPatch.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAGA,IAAAA,QAAA,GAAAC,uBAAA,CAAAC,OAAA;AAAkE,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAHlE;;;;AA2DA;;;;AAIO,MAAMkB,KAAK,GAAAC,OAAA,CAAAD,KAAA,GAActB,QAAQ,CAACsB,KAAK;AAE9C;;;;AAIO,MAAME,OAAO,GAAAD,OAAA,CAAAC,OAAA,GAWhBxB,QAAQ,CAACwB,OAAO;AAEpB;;;;AAIO,MAAMC,OAAO,GAAAF,OAAA,CAAAE,OAAA,GAWhBzB,QAAQ,CAACyB,OAAO;AAEpB;;;;AAIO,MAAMC,MAAM,GAAAH,OAAA,CAAAG,MAAA,GAWf1B,QAAQ,CAAC0B,MAAM;AAEnB;;;;AAIO,MAAMC,QAAQ,GAAAJ,OAAA,CAAAI,QAAA,GAWjB3B,QAAQ,CAAC2B,QAAQ", "ignoreList": []}