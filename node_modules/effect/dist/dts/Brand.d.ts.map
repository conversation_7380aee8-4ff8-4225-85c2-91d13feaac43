{"version": 3, "file": "Brand.d.ts", "sourceRoot": "", "sources": ["../../src/Brand.ts"], "names": [], "mappings": "AAmBA,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAA;AAC/C,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,OAAO,MAAmC,CAAA;AAEpE;;;GAGG;AACH,MAAM,MAAM,WAAW,GAAG,OAAO,WAAW,CAAA;AAE5C;;;GAGG;AACH,eAAO,MAAM,yBAAyB,EAAE,OAAO,MAA2C,CAAA;AAE1F;;;GAGG;AACH,MAAM,MAAM,yBAAyB,GAAG,OAAO,yBAAyB,CAAA;AAExE;;;;;GAKG;AACH,MAAM,WAAW,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM;IACrD,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE;QACtB,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;KACrB,CAAA;CACF;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B;;;;;OAKG;IACH,UAAiB,WAAY,SAAQ,KAAK,CAAC,eAAe,CAAC;KAAG;IAE9D;;;;;OAKG;IACH,UAAiB,eAAe;QAC9B,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAA;QACtB,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;KACzB;IAED;;;OAGG;IACH,UAAiB,WAAW,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC;QACtD,QAAQ,CAAC,CAAC,yBAAyB,CAAC,EAAE,yBAAyB,CAAA;QAC/D;;;WAGG;QACH,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QAC7B;;;WAGG;QACH,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAClD;;;WAGG;QACH,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,CAAA;QACrE;;;WAGG;QACH,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;KACvD;IAED;;;;;OAKG;IACH,KAAY,eAAe,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;IAEjF;;;;;OAKG;IACH,KAAY,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IAEhE;;;;;OAKG;IACH,KAAY,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,mBAAmB,CACpE;SACG,CAAC,IAAI,MAAM,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,GAC7D,KAAK;KACV,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,CACxB,GACC,KAAK,CAAA;IAET;;;;;OAKG;IACH,KAAY,gBAAgB,CAC1B,MAAM,SAAS,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAChF;SACD,CAAC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SACpE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAC/C,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GACzG,MAAM,CAAC,CAAC,CAAC,GACX,MAAM,CAAC,CAAC,CAAC,GACT,kDAAkD;KACvD,CAAA;CACF;AAED;;;GAGG;AACH,MAAM,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,GAAG,MAAM,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;AAEhE;;;;;GAKG;AACH,eAAO,MAAM,KAAK,GAAI,SAAS,MAAM,EAAE,OAAO,OAAO,KAAG,KAAK,CAAC,WAG5D,CAAA;AAEF;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC,WAEzB,CAAA;AAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EAC1C,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GACrE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AACvB,wBAAgB,OAAO,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EAC1C,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACzC,SAAS,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,WAAW,GAC9D,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;AAuBvB;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,eAAO,MAAM,OAAO,GAAI,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,OAAK,KAAK,CAAC,WAAW,CAChE,CAAC,CASF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,eAAO,MAAM,GAAG,EAAE,CAAC,MAAM,SAAS,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EACnG,GAAG,MAAM,EAAE,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,KACtC,KAAK,CAAC,WAAW,CACpB,KAAK,CAAC,mBAAmB,CAAC;KAAG,CAAC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;CAAE,CAAC,MAAM,CAAC,CAAC,SAC1F,MAAM,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAmC9C,CAAA;AAED;;;;;GAKG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE,CAAC,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,CAAgB,CAAA"}