{"version": 3, "file": "Equivalence.d.ts", "sourceRoot": "", "sources": ["../../src/Equivalence.ts"], "names": [], "mappings": "AAQA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,UAAU,CAAA;AAE1C;;;GAGG;AACH,MAAM,WAAW,WAAW,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,GAAG,OAAO,CAAA;CAC5B;AAED;;;GAGG;AACH,MAAM,WAAW,qBAAsB,SAAQ,UAAU;IACvD,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;CAC3C;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,KAAK,OAAO,KAAG,WAAW,CAAC,CAAC,CACxC,CAAA;AAI3C;;;;;GAKG;AACH,eAAO,MAAM,MAAM,EAAE,CAAC,CAAC,OAAO,WAAW,CAAC,CAAC,CAA4B,CAAA;AAEvE;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,WAAW,CAAC,MAAM,CAAY,CAAA;AAEnD;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,WAAW,CAAC,MAAM,CAAY,CAAA;AAEnD;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE,WAAW,CAAC,OAAO,CAAY,CAAA;AAErD;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,WAAW,CAAC,MAAM,CAAY,CAAA;AAEnD;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,WAAW,CAAC,MAAM,CAAY,CAAA;AAEnD;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAA;IACnE;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;CACuD,CAAA;AAExH;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE;IACxB;;;OAGG;IACH,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAA;IACnF;;;OAGG;IACH,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;CAY5E,CAAA;AAIL;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,YAAY,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAG,WAAW,CAAC,CAAC,CACrC,CAAA;AAE7C;;;GAGG;AACH,eAAO,MAAM,QAAQ,EAAE;IACrB;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,CAAC,CAAC,CAAA;IAChE;;;OAGG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;CAI7D,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,IAAI,EAAE,WAAW,CAAC,IAAI,CAA8C,CAAA;AAEjF;;;GAGG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACpF,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;CAKjF,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,GAAG,GAAI,CAAC,EAAE,YAAY,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KAAG,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAgBzF,CAAA;AAED;;;GAGG;AACH,eAAO,MAAM,WAAW,GAAI,CAAC,EAC3B,MAAM,WAAW,CAAC,CAAC,CAAC,EACpB,YAAY,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,KACnC,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAGvC,CAAA;AAED;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,SAAS,aAAa,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,EAC7D,GAAG,UAAU,CAAC,KACb,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAE,CAAC,CAAyB,CAAA;AAEvH;;;;;GAKG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,CAAC,KAAG,WAAW,CAAC,aAAa,CAAC,CAAC,CAAC,CAcxE,CAAA;AAEJ;;;;;;GAMG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,EAC/D,QAAQ,CAAC,KACR,WAAW,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAE,CAU5F,CAAA"}