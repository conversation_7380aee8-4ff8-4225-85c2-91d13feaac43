{"version": 3, "file": "FiberHandle.d.ts", "sourceRoot": "", "sources": ["../../src/FiberHandle.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAExD,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAA;AACzC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AAErC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AAGvC,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAC/C,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,EAAE,KAAK,QAAQ,EAAiB,MAAM,eAAe,CAAA;AAE5D,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,KAAK,MAAM,YAAY,CAAA;AAExC;;;GAGG;AACH,eAAO,MAAM,MAAM,EAAE,OAAO,MAAyC,CAAA;AAErE;;;GAGG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,MAAM,CAAA;AAElC;;;GAGG;AACH,MAAM,WAAW,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,OAAO,CAAE,SAAQ,QAAQ,EAAE,WAAW,CAAC,WAAW;IACtG,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAA;IACzB,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;CAQpD;AAED;;;GAGG;AACH,eAAO,MAAM,aAAa,GAAI,GAAG,OAAO,KAAG,CAAC,IAAI,WAA+C,CAAA;AA8B/F;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,KAAK,CAelG,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,WAAW,GAAI,CAAC,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EACvE,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAC7C,IACC,SAAS,KACV,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAEH;;;;;GAKG;AACH,eAAO,MAAM,kBAAkB,GAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,OAAO,OAAK,MAAM,CAAC,MAAM,EACtF,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE,OAAO,CAAC,cAAc,GAAG,SAAS,KACzC,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,KAAK,CAAC,KAAK,GAAG,CAAC,CAKd,CAAA;AAaH;;;;;;GAMG;AACH,eAAO,MAAM,SAAS,EAAE;IACtB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAA;IACpC;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EACvB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClD,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,IAAI,CAAA;CAwCP,CAAA;AAEF;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAA;QAChC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IACnD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EACvB,KAAK,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EACjC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAA;QAChC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;CAkBpB,CAAA;AAEJ;;;;;GAKG;AACH,eAAO,MAAM,SAAS,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CACV,CAAA;AAEtF;;;;;GAKG;AACH,eAAO,MAAM,GAAG,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,sBAAsB,CAC3E,CAAA;AAEvC;;;GAGG;AACH,eAAO,MAAM,KAAK,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAerE,CAAA;AAYH;;;;;;GAMG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EACvB,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAA;QAChC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAC/B,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,KAC7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;IACxD;;;;;;OAMG;IACH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EAClC,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,EACvB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EAAE;QACR,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAA;QAChC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KACrD,GACA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAA;CAQvD,CAAA;AAsBD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,eAAO,MAAM,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EACzB,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KACpB,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EACjC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACJ,OAAO,CAAC,cAAc,GAAG;IACzB,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC5C,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CACrD,GACC,SAAS,KACV,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAC/B,KAAK,EACL,CAAC,CAyBA,CAAA;AAEH;;;;;;;;GAQG;AACH,eAAO,MAAM,cAAc,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,CAAC,CAAC,GAAG,KAAK,OAAO,MAAM,CAAC,MAAM,EAC3F,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChC,OAAO,CAAC,EACN,CAAE,OAAO,CAAC,cAAc,GAAG;IAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;CAAE,IACjF,SAAS,KACV,OAAO,CAAC,EAAE,CAAC,GAChB,KAAK,EACL,CAAC,CAqBA,CAAA;AAEH;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CACb,CAAA;AAE7D;;;;;GAKG;AACH,eAAO,MAAM,UAAU,GAAI,CAAC,EAAE,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,KAAG,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAM5E,CAAA"}