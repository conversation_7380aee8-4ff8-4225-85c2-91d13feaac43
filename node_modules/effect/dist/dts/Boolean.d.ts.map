{"version": 3, "file": "Boolean.d.ts", "sourceRoot": "", "sources": ["../../src/Boolean.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,OAAO,KAAK,WAAW,MAAM,kBAAkB,CAAA;AAC/C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAA;AAE5C,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AAGnC;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,SAAS,EAAE,CAAC,KAAK,EAAE,OAAO,KAAK,KAAK,IAAI,OAA6B,CAAA;AAElF;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,KAAK,EAAE;IAClB;;;;;;;;;;;;;;OAcG;IACH,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EACP,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;QAC5B,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;KAC5B,GACA,CAAC,KAAK,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAA;IAC5B;;;;;;;;;;;;;;OAcG;IACH,CAAC,CAAC,EAAE,CAAC,EACH,KAAK,EAAE,OAAO,EACd,OAAO,EAAE;QACP,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;QAC5B,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAA;KAC5B,GACA,CAAC,GAAG,CAAC,CAAA;CAIgD,CAAA;AAE1D;;;GAGG;AACH,eAAO,MAAM,WAAW,EAAE,WAAW,CAAC,WAAW,CAAC,OAAO,CAAuB,CAAA;AAEhF;;;GAGG;AACH,eAAO,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAiB,CAAA;AAExD;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,GAAG,GAAI,MAAM,OAAO,KAAG,OAAgB,CAAA;AAEpD;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CAC2B,CAAA;AAEpE;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,IAAI,EAAE;IACjB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CAC8B,CAAA;AAEvE;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,EAAE,EAAE;IACf;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CAC2B,CAAA;AAEpE;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CAC8B,CAAA;AAEvE;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CACiD,CAAA;AAE1F;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,GAAG,EAAE;IAChB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CAC+B,CAAA;AAExE;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,OAAO,EAAE;IACpB;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,CAAA;IAC3C;;;;;;;;;;;;;;;;OAgBG;IACH,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,GAAG,OAAO,CAAA;CACM,CAAA;AAE/C;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,KAAK,GAAI,YAAY,QAAQ,CAAC,OAAO,CAAC,KAAG,OAOrD,CAAA;AAED;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAM,IAAI,GAAI,YAAY,QAAQ,CAAC,OAAO,CAAC,KAAG,OAOpD,CAAA"}