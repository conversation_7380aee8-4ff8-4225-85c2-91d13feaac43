import * as internal from "./internal/metric/keyType.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const MetricKeyTypeTypeId = internal.MetricKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const CounterKeyTypeTypeId = internal.CounterKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const FrequencyKeyTypeTypeId = internal.FrequencyKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const GaugeKeyTypeTypeId = internal.GaugeKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const HistogramKeyTypeTypeId = internal.HistogramKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const SummaryKeyTypeTypeId = internal.SummaryKeyTypeTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const counter = internal.counter;
/**
 * @since 2.0.0
 * @category constructors
 */
export const frequency = internal.frequency;
/**
 * @since 2.0.0
 * @category constructors
 */
export const gauge = internal.gauge;
/**
 * @since 2.0.0
 * @category constructors
 */
export const histogram = internal.histogram;
/**
 * @since 2.0.0
 * @category constructors
 */
export const summary = internal.summary;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isMetricKeyType = internal.isMetricKeyType;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isCounterKey = internal.isCounterKey;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isFrequencyKey = internal.isFrequencyKey;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isGaugeKey = internal.isGaugeKey;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isHistogramKey = internal.isHistogramKey;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isSummaryKey = internal.isSummaryKey;
//# sourceMappingURL=MetricKeyType.js.map