/**
 * @since 2.0.0
 */
import * as internal from "./internal/schedule/decision.js";
const _continue = internal._continue;
export {
/**
 * @since 2.0.0
 * @category constructors
 */
_continue as continue };
/**
 * @since 2.0.0
 * @category constructors
 */
export const continueWith = internal.continueWith;
/**
 * @since 2.0.0
 * @category constructors
 */
export const done = internal.done;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isContinue = internal.isContinue;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isDone = internal.isDone;
//# sourceMappingURL=ScheduleDecision.js.map