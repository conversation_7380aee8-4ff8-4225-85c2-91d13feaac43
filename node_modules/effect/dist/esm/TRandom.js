import * as internal from "./internal/stm/tRandom.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const TRandomTypeId = internal.TRandomTypeId;
/**
 * The service tag used to access `TRandom` in the environment of an effect.
 *
 * @since 2.0.0
 * @category context
 */
export const Tag = internal.Tag;
/**
 * The "live" `TRandom` service wrapped into a `Layer`.
 *
 * @since 2.0.0
 * @category context
 */
export const live = internal.live;
/**
 * Returns the next number from the pseudo-random number generator.
 *
 * @since 2.0.0
 * @category random
 */
export const next = internal.next;
/**
 * Returns the next boolean value from the pseudo-random number generator.
 *
 * @since 2.0.0
 * @category random
 */
export const nextBoolean = internal.nextBoolean;
/**
 * Returns the next integer from the pseudo-random number generator.
 *
 * @since 2.0.0
 * @category random
 */
export const nextInt = internal.nextInt;
/**
 * Returns the next integer in the specified range from the pseudo-random number
 * generator.
 *
 * @since 2.0.0
 * @category random
 */
export const nextIntBetween = internal.nextIntBetween;
/**
 * Returns the next number in the specified range from the pseudo-random number
 * generator.
 *
 * @since 2.0.0
 * @category random
 */
export const nextRange = internal.nextRange;
/**
 * Uses the pseudo-random number generator to shuffle the specified iterable.
 *
 * @since 2.0.0
 * @category random
 */
export const shuffle = internal.shuffle;
//# sourceMappingURL=TRandom.js.map