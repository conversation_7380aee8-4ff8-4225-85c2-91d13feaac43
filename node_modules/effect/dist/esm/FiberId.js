import * as internal from "./internal/fiberId.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const FiberIdTypeId = internal.FiberIdTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const none = internal.none;
/**
 * @since 2.0.0
 * @category constructors
 */
export const runtime = internal.runtime;
/**
 * @since 2.0.0
 * @category constructors
 */
export const composite = internal.composite;
/**
 * Returns `true` if the specified unknown value is a `FiberId`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isFiberId = internal.isFiberId;
/**
 * Returns `true` if the `FiberId` is a `None`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isNone = internal.isNone;
/**
 * Returns `true` if the `FiberId` is a `Runtime`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isRuntime = internal.isRuntime;
/**
 * Returns `true` if the `FiberId` is a `Composite`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isComposite = internal.isComposite;
/**
 * Combine two `FiberId`s.
 *
 * @since 2.0.0
 * @category constructors
 */
export const combine = internal.combine;
/**
 * Combines a set of `FiberId`s into a single `FiberId`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const combineAll = internal.combineAll;
/**
 * Returns this `FiberId` if it is not `None`, otherwise returns that `FiberId`.
 *
 * @since 2.0.0
 * @category utils
 */
export const getOrElse = internal.getOrElse;
/**
 * Get the set of identifiers for this `FiberId`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const ids = internal.ids;
/**
 * Creates a new `FiberId`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Creates a string representing the name of the current thread of execution
 * represented by the specified `FiberId`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const threadName = internal.threadName;
/**
 * Convert a `FiberId` into an `Option<FiberId>`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const toOption = internal.toOption;
/**
 * Convert a `FiberId` into a `HashSet<FiberId>`.
 *
 * @since 2.0.0
 * @category destructors
 */
export const toSet = internal.toSet;
/**
 * Unsafely creates a new `FiberId`.
 *
 * @since 2.0.0
 * @category unsafe
 */
export const unsafeMake = internal.unsafeMake;
//# sourceMappingURL=FiberId.js.map