import * as internal from "./internal/metric/key.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const MetricKeyTypeId = internal.MetricKeyTypeId;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isMetricKey = internal.isMetricKey;
/**
 * Creates a metric key for a counter, with the specified name.
 *
 * @since 2.0.0
 * @category constructors
 */
export const counter = internal.counter;
/**
 * Creates a metric key for a categorical frequency table, with the specified
 * name.
 *
 * @since 2.0.0
 * @category constructors
 */
export const frequency = internal.frequency;
/**
 * Creates a metric key for a gauge, with the specified name.
 *
 * @since 2.0.0
 * @category constructors
 */
export const gauge = internal.gauge;
/**
 * Creates a metric key for a histogram, with the specified name and boundaries.
 *
 * @since 2.0.0
 * @category constructors
 */
export const histogram = internal.histogram;
/**
 * Creates a metric key for a summary, with the specified name, maxAge,
 * maxSize, error, and quantiles.
 *
 * @since 2.0.0
 * @category constructors
 */
export const summary = internal.summary;
/**
 * Returns a new `Metric<PERSON>ey` with the specified tag appended.
 *
 * @since 2.0.0
 * @category constructors
 */
export const tagged = internal.tagged;
/**
 * Returns a new `MetricKey` with the specified tags appended.
 *
 * @since 2.0.0
 * @category constructors
 */
export const taggedWithLabels = internal.taggedWithLabels;
//# sourceMappingURL=MetricKey.js.map