{"version": 3, "file": "Equal.js", "names": ["Hash", "hasProperty", "structuralRegionState", "symbol", "Symbol", "for", "equals", "arguments", "length", "self", "compareBoth", "that", "selfType", "isEqual", "hash", "enabled", "tester", "Date", "toISOString", "URL", "href", "Array", "isArray", "every", "v", "i", "Object", "getPrototypeOf", "prototype", "keysSelf", "keys", "keysThat", "key", "u", "equivalence"], "sources": ["../../src/Equal.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,IAAI,MAAM,WAAW;AACjC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,qBAAqB,QAAQ,YAAY;AAElD;;;;AAIA,OAAO,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,cAAc,CAAC;AAgB/D,OAAM,SAAUC,MAAMA,CAAA;EACpB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAQC,IAAa,IAAKC,WAAW,CAACD,IAAI,EAAEF,SAAS,CAAC,CAAC,CAAC,CAAC;EAC3D;EACA,OAAOG,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC;AAChD;AAEA,SAASG,WAAWA,CAACD,IAAa,EAAEE,IAAa;EAC/C,IAAIF,IAAI,KAAKE,IAAI,EAAE;IACjB,OAAO,IAAI;EACb;EACA,MAAMC,QAAQ,GAAG,OAAOH,IAAI;EAC5B,IAAIG,QAAQ,KAAK,OAAOD,IAAI,EAAE;IAC5B,OAAO,KAAK;EACd;EACA,IAAIC,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;IACpD,IAAIH,IAAI,KAAK,IAAI,IAAIE,IAAI,KAAK,IAAI,EAAE;MAClC,IAAIE,OAAO,CAACJ,IAAI,CAAC,IAAII,OAAO,CAACF,IAAI,CAAC,EAAE;QAClC,IAAIX,IAAI,CAACc,IAAI,CAACL,IAAI,CAAC,KAAKT,IAAI,CAACc,IAAI,CAACH,IAAI,CAAC,IAAIF,IAAI,CAACN,MAAM,CAAC,CAACQ,IAAI,CAAC,EAAE;UAC7D,OAAO,IAAI;QACb,CAAC,MAAM;UACL,OAAOT,qBAAqB,CAACa,OAAO,IAAIb,qBAAqB,CAACc,MAAM,GAChEd,qBAAqB,CAACc,MAAM,CAACP,IAAI,EAAEE,IAAI,CAAC,GACxC,KAAK;QACX;MACF,CAAC,MAAM,IAAIF,IAAI,YAAYQ,IAAI,IAAIN,IAAI,YAAYM,IAAI,EAAE;QACvD,OAAOR,IAAI,CAACS,WAAW,EAAE,KAAKP,IAAI,CAACO,WAAW,EAAE;MAClD,CAAC,MAAM,IAAIT,IAAI,YAAYU,GAAG,IAAIR,IAAI,YAAYQ,GAAG,EAAE;QACrD,OAAOV,IAAI,CAACW,IAAI,KAAKT,IAAI,CAACS,IAAI;MAChC;IACF;IACA,IAAIlB,qBAAqB,CAACa,OAAO,EAAE;MACjC,IAAIM,KAAK,CAACC,OAAO,CAACb,IAAI,CAAC,IAAIY,KAAK,CAACC,OAAO,CAACX,IAAI,CAAC,EAAE;QAC9C,OAAOF,IAAI,CAACD,MAAM,KAAKG,IAAI,CAACH,MAAM,IAAIC,IAAI,CAACc,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKf,WAAW,CAACc,CAAC,EAAEb,IAAI,CAACc,CAAC,CAAC,CAAC,CAAC;MACrF;MACA,IAAIC,MAAM,CAACC,cAAc,CAAClB,IAAI,CAAC,KAAKiB,MAAM,CAACE,SAAS,IAAIF,MAAM,CAACC,cAAc,CAAClB,IAAI,CAAC,KAAKiB,MAAM,CAACE,SAAS,EAAE;QACxG,MAAMC,QAAQ,GAAGH,MAAM,CAACI,IAAI,CAACrB,IAAW,CAAC;QACzC,MAAMsB,QAAQ,GAAGL,MAAM,CAACI,IAAI,CAACnB,IAAW,CAAC;QACzC,IAAIkB,QAAQ,CAACrB,MAAM,KAAKuB,QAAQ,CAACvB,MAAM,EAAE;UACvC,KAAK,MAAMwB,GAAG,IAAIH,QAAQ,EAAE;YAC1B;YACA,IAAI,EAAEG,GAAG,IAAIrB,IAAI,IAAID,WAAW,CAACD,IAAI,CAACuB,GAAG,CAAC,EAAErB,IAAI,CAACqB,GAAG,CAAC,CAAC,CAAC,EAAE;cACvD,OAAO9B,qBAAqB,CAACc,MAAM,GAAGd,qBAAqB,CAACc,MAAM,CAACP,IAAI,EAAEE,IAAI,CAAC,GAAG,KAAK;YACxF;UACF;UACA,OAAO,IAAI;QACb;MACF;MACA,OAAOT,qBAAqB,CAACc,MAAM,GAAGd,qBAAqB,CAACc,MAAM,CAACP,IAAI,EAAEE,IAAI,CAAC,GAAG,KAAK;IACxF;EACF;EAEA,OAAOT,qBAAqB,CAACa,OAAO,IAAIb,qBAAqB,CAACc,MAAM,GAChEd,qBAAqB,CAACc,MAAM,CAACP,IAAI,EAAEE,IAAI,CAAC,GACxC,KAAK;AACX;AAEA;;;;AAIA,OAAO,MAAME,OAAO,GAAIoB,CAAU,IAAiBhC,WAAW,CAACgC,CAAC,EAAE9B,MAAM,CAAC;AAEzE;;;;AAIA,OAAO,MAAM+B,WAAW,GAA4BA,CAAA,KAAM5B,MAAM", "ignoreList": []}