import * as internal from "./internal/metric/hook.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const MetricHookTypeId = internal.MetricHookTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * @since 2.0.0
 * @category constructors
 */
export const counter = internal.counter;
/**
 * @since 2.0.0
 * @category constructors
 */
export const frequency = internal.frequency;
/**
 * @since 2.0.0
 * @category constructors
 */
export const gauge = internal.gauge;
/**
 * @since 2.0.0
 * @category constructors
 */
export const histogram = internal.histogram;
/**
 * @since 2.0.0
 * @category constructors
 */
export const summary = internal.summary;
/**
 * @since 2.0.0
 * @category utils
 */
export const onUpdate = internal.onUpdate;
/**
 * @since 3.6.5
 * @category utils
 */
export const onModify = internal.onModify;
//# sourceMappingURL=MetricHook.js.map