{"version": 3, "file": "GroupBy.js", "names": ["internal", "GroupByTypeId", "evaluate", "filter", "first", "make"], "sources": ["../../src/GroupBy.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,QAAQ,MAAM,uBAAuB;AAQjD;;;;AAIA,OAAO,MAAMC,aAAa,GAAkBD,QAAQ,CAACC,aAAa;AAsClE;;;;;;;AAOA,OAAO,MAAMC,QAAQ,GAwBjBF,QAAQ,CAACE,QAAQ;AAErB;;;;;;AAMA,OAAO,MAAMC,MAAM,GAefH,QAAQ,CAACG,MAAM;AAEnB;;;;;;AAMA,OAAO,MAAMC,KAAK,GAedJ,QAAQ,CAACI,KAAK;AAElB;;;;;;AAMA,OAAO,MAAMC,IAAI,GAEUL,QAAQ,CAACK,IAAI", "ignoreList": []}