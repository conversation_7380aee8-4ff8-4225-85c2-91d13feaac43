/**
 * @since 2.0.0
 */
import * as internal from "./internal/channel/upstreamPullStrategy.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const UpstreamPullStrategyTypeId = internal.UpstreamPullStrategyTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const PullAfterNext = internal.PullAfterNext;
/**
 * @since 2.0.0
 * @category constructors
 */
export const PullAfterAllEnqueued = internal.PullAfterAllEnqueued;
/**
 * Returns `true` if the specified value is an `UpstreamPullStrategy`, `false`
 * otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isUpstreamPullStrategy = internal.isUpstreamPullStrategy;
/**
 * Returns `true` if the specified `UpstreamPullStrategy` is a `PullAfterNext`,
 * `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isPullAfterNext = internal.isPullAfterNext;
/**
 * Returns `true` if the specified `UpstreamPullStrategy` is a
 * `PullAfterAllEnqueued`, `false` otherwise.
 *
 * @since 2.0.0
 * @category refinements
 */
export const isPullAfterAllEnqueued = internal.isPullAfterAllEnqueued;
/**
 * Folds an `UpstreamPullStrategy<A>` into a value of type `Z`.
 *
 * @since 2.0.0
 * @category folding
 */
export const match = internal.match;
//# sourceMappingURL=UpstreamPullStrategy.js.map