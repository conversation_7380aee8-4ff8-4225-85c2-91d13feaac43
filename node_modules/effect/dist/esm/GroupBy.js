/**
 * @since 2.0.0
 */
import * as internal from "./internal/groupBy.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const GroupByTypeId = internal.GroupByTypeId;
/**
 * Run the function across all groups, collecting the results in an
 * arbitrary order.
 *
 * @since 2.0.0
 * @category destructors
 */
export const evaluate = internal.evaluate;
/**
 * Filter the groups to be processed.
 *
 * @since 2.0.0
 * @category utils
 */
export const filter = internal.filter;
/**
 * Only consider the first `n` groups found in the `Stream`.
 *
 * @since 2.0.0
 * @category utils
 */
export const first = internal.first;
/**
 * Constructs a `GroupBy` from a `Stream`.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
//# sourceMappingURL=GroupBy.js.map