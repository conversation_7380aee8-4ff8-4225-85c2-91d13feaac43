import * as core from "./internal/core.js";
import * as fiberRuntime from "./internal/fiberRuntime.js";
import * as query from "./internal/query.js";
import * as Scheduler from "./Scheduler.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const FiberRefTypeId = core.FiberRefTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const make = fiberRuntime.fiberRefMake;
/**
 * @since 2.0.0
 * @category constructors
 */
export const makeWith = fiberRuntime.fiberRefMakeWith;
/**
 * @since 2.0.0
 * @category constructors
 */
export const makeContext = fiberRuntime.fiberRefMakeContext;
/**
 * @since 2.0.0
 * @category constructors
 */
export const makeRuntimeFlags = fiberRuntime.fiberRefMakeRuntimeFlags;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unsafeMake = core.fiberRefUnsafeMake;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unsafeMakeHashSet = core.fiberRefUnsafeMakeHashSet;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unsafeMakeContext = core.fiberRefUnsafeMakeContext;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unsafeMakeSupervisor = fiberRuntime.fiberRefUnsafeMakeSupervisor;
/**
 * @since 2.0.0
 * @category constructors
 */
export const unsafeMakePatch = core.fiberRefUnsafeMakePatch;
/**
 * @since 2.0.0
 * @category getters
 */
export const get = core.fiberRefGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndSet = core.fiberRefGetAndSet;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdate = core.fiberRefGetAndUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
export const getAndUpdateSome = core.fiberRefGetAndUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const getWith = core.fiberRefGetWith;
/**
 * @since 2.0.0
 * @category utils
 */
export const set = core.fiberRefSet;
const _delete = core.fiberRefDelete;
export {
/**
 * @since 2.0.0
 * @category utils
 */
_delete as delete };
/**
 * @since 2.0.0
 * @category utils
 */
export const reset = core.fiberRefReset;
/**
 * @since 2.0.0
 * @category utils
 */
export const modify = core.fiberRefModify;
/**
 * @since 2.0.0
 * @category utils
 */
export const modifySome = core.fiberRefModifySome;
/**
 * @since 2.0.0
 * @category utils
 */
export const update = core.fiberRefUpdate;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSome = core.fiberRefUpdateSome;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateAndGet = core.fiberRefUpdateAndGet;
/**
 * @since 2.0.0
 * @category utils
 */
export const updateSomeAndGet = core.fiberRefUpdateSomeAndGet;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentConcurrency = core.currentConcurrency;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentRequestBatchingEnabled = core.currentRequestBatching;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentRequestCache = query.currentCache;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentRequestCacheEnabled = query.currentCacheEnabled;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentContext = core.currentContext;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentSchedulingPriority = core.currentSchedulingPriority;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentMaxOpsBeforeYield = core.currentMaxOpsBeforeYield;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const unhandledErrorLogLevel = core.currentUnhandledErrorLogLevel;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentLogAnnotations = core.currentLogAnnotations;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentLoggers = fiberRuntime.currentLoggers;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentLogLevel = core.currentLogLevel;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentMinimumLogLevel = fiberRuntime.currentMinimumLogLevel;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentLogSpan = core.currentLogSpan;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentRuntimeFlags = fiberRuntime.currentRuntimeFlags;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentScheduler = Scheduler.currentScheduler;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentSupervisor = fiberRuntime.currentSupervisor;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentMetricLabels = core.currentMetricLabels;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentTracerEnabled = core.currentTracerEnabled;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentTracerTimingEnabled = core.currentTracerTimingEnabled;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentTracerSpanAnnotations = core.currentTracerSpanAnnotations;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const currentTracerSpanLinks = core.currentTracerSpanLinks;
/**
 * @since 2.0.0
 * @category fiberRefs
 */
export const interruptedCause = core.currentInterruptedCause;
//# sourceMappingURL=FiberRef.js.map