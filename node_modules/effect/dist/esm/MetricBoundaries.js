import * as internal from "./internal/metric/boundaries.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const MetricBoundariesTypeId = internal.MetricBoundariesTypeId;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isMetricBoundaries = internal.isMetricBoundaries;
/**
 * @since 2.0.0
 * @category constructors
 */
export const fromIterable = internal.fromIterable;
/**
 * A helper method to create histogram bucket boundaries for a histogram
 * with linear increasing values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const linear = internal.linear;
/**
 * A helper method to create histogram bucket boundaries for a histogram
 * with exponentially increasing values.
 *
 * @since 2.0.0
 * @category constructors
 */
export const exponential = internal.exponential;
//# sourceMappingURL=MetricBoundaries.js.map