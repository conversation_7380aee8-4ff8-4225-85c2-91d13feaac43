{"version": 3, "file": "fiber.js", "names": ["Clock", "Either", "Exit", "FiberId", "FiberStatus", "dual", "pipe", "HashSet", "number", "Option", "order", "pipeArguments", "hasProperty", "core", "effectable", "fiberScope", "runtimeFlags", "FiberSymbolKey", "FiberTypeId", "Symbol", "for", "fiberVariance", "_E", "_", "_A", "fiberProto", "arguments", "RuntimeFiberSymbolKey", "RuntimeFiberTypeId", "Order", "tuple", "mapInput", "fiber", "id", "startTimeMillis", "isFiber", "u", "isRuntimeFiber", "self", "_await", "await", "children", "done", "exit", "_fiber", "CommitPrototype", "commit", "join", "none", "succeed", "inheritAll", "void", "poll", "some", "interruptAsFork", "dump", "map", "status", "dumpAll", "fibers", "forEachSequential", "fail", "error", "failCause", "cause", "fromEffect", "effect", "interrupted", "fiberId", "interrupt", "interruptAll", "flatMap", "interruptAllAs", "forEachSequentialDiscard", "zipRight", "zipLeft", "flatten", "f", "mapEffect", "a", "sync", "forEachEffect", "result", "_tag", "value", "mapFiber", "match", "onFailure", "onSuccess", "onFiber", "onRuntimeFiber", "_never", "never", "orElse", "that", "getOr<PERSON><PERSON>e", "zipWith", "exit1", "exit2", "isSuccess", "option1", "option2", "interruptAsFiber", "asVoid", "or<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "left", "right", "parseMs", "milliseconds", "roundTowardsZero", "Math", "floor", "ceil", "days", "hours", "minutes", "seconds", "microseconds", "nanoseconds", "renderStatus", "isDone", "isRunning", "isInterruptible", "interruptible", "pretty", "currentTimeMillis", "now", "time", "lifeMsg", "waitMsg", "isSuspended", "ids", "blockingOn", "size", "Array", "from", "statusMsg", "unsafeRoots", "globalScope", "roots", "void_", "currentFiberURI", "getCurrentFiber", "fromNullable", "globalThis"], "sources": ["../../../src/internal/fiber.ts"], "sourcesContent": [null], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,aAAa;AAEpC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,WAAW,MAAM,mBAAmB;AAChD,SAASC,IAAI,EAAEC,IAAI,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,OAAO,MAAM,eAAe;AACxC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,MAAM,MAAM,cAAc;AACtC,OAAO,KAAKC,KAAK,MAAM,aAAa;AACpC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,UAAU,MAAM,iBAAiB;AAC7C,OAAO,KAAKC,YAAY,MAAM,mBAAmB;AAEjD;AACA,MAAMC,cAAc,GAAG,cAAc;AAErC;AACA,OAAO,MAAMC,WAAW,gBAAsBC,MAAM,CAACC,GAAG,CACtDH,cAAc,CACM;AAEtB;AACA,OAAO,MAAMI,aAAa,GAAG;EAC3B;EACAC,EAAE,EAAGC,CAAQ,IAAKA,CAAC;EACnB;EACAC,EAAE,EAAGD,CAAQ,IAAKA;CACnB;AAED;AACA,MAAME,UAAU,GAAG;EACjB,CAACP,WAAW,GAAGG,aAAa;EAC5Bf,IAAIA,CAAA;IACF,OAAOK,aAAa,CAAC,IAAI,EAAEe,SAAS,CAAC;EACvC;CACD;AAED;AACA,MAAMC,qBAAqB,GAAG,cAAc;AAE5C;AACA,OAAO,MAAMC,kBAAkB,gBAA6BT,MAAM,CAACC,GAAG,CACpEO,qBAAqB,CACM;AAE7B;AACA,OAAO,MAAME,KAAK,gBAAsDvB,IAAI,cAC1EI,KAAK,CAACoB,KAAK,CAACtB,MAAM,CAACqB,KAAK,EAAErB,MAAM,CAACqB,KAAK,CAAC,eACvCnB,KAAK,CAACqB,QAAQ,CAAEC,KAA2C,IACzD,CACGA,KAAK,CAACC,EAAE,EAAsB,CAACC,eAAe,EAC9CF,KAAK,CAACC,EAAE,EAAsB,CAACA,EAAE,CAC1B,CACX,CACF;AAED;AACA,OAAO,MAAME,OAAO,GAAIC,CAAU,IAAyCxB,WAAW,CAACwB,CAAC,EAAElB,WAAW,CAAC;AAEtG;AACA,OAAO,MAAMmB,cAAc,GAAUC,IAAuB,IAC1DV,kBAAkB,IAAIU,IAAI;AAE5B;AACA,OAAO,MAAMC,MAAM,GAAUD,IAAuB,IAAqCA,IAAI,CAACE,KAAK;AAEnG;AACA,OAAO,MAAMC,QAAQ,GACnBH,IAAuB,IACgCA,IAAI,CAACG,QAAQ;AAEtE;AACA,OAAO,MAAMC,IAAI,GAAUC,IAAqB,IAAuB;EACrE,MAAMC,MAAM,GAAG;IACb,GAAG9B,UAAU,CAAC+B,eAAe;IAC7BC,MAAMA,CAAA;MACJ,OAAOC,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,GAAGtB,UAAU;IACbQ,EAAE,EAAEA,CAAA,KAAM9B,OAAO,CAAC6C,IAAI;IACtBR,KAAK,EAAE3B,IAAI,CAACoC,OAAO,CAACN,IAAI,CAAC;IACzBF,QAAQ,EAAE5B,IAAI,CAACoC,OAAO,CAAC,EAAE,CAAC;IAC1BC,UAAU,EAAErC,IAAI,CAACsC,IAAI;IACrBC,IAAI,EAAEvC,IAAI,CAACoC,OAAO,CAACxC,MAAM,CAAC4C,IAAI,CAACV,IAAI,CAAC,CAAC;IACrCW,eAAe,EAAEA,CAAA,KAAMzC,IAAI,CAACsC;GAC7B;EAED,OAAOP,MAAM;AACf,CAAC;AAED;AACA,OAAO,MAAMW,IAAI,GAAUjB,IAA8B,IACvDzB,IAAI,CAAC2C,GAAG,CAAClB,IAAI,CAACmB,MAAM,EAAGA,MAAM,KAAM;EAAExB,EAAE,EAAEK,IAAI,CAACL,EAAE,EAAE;EAAEwB;AAAM,CAAE,CAAC,CAAC;AAEhE;AACA,OAAO,MAAMC,OAAO,GAClBC,MAAsD,IACX9C,IAAI,CAAC+C,iBAAiB,CAACD,MAAM,EAAEJ,IAAI,CAAC;AAEjF;AACA,OAAO,MAAMM,IAAI,GAAOC,KAAQ,IAA4BpB,IAAI,CAACxC,IAAI,CAAC2D,IAAI,CAACC,KAAK,CAAC,CAAC;AAElF;AACA,OAAO,MAAMC,SAAS,GAAOC,KAAqB,IAA4BtB,IAAI,CAACxC,IAAI,CAAC6D,SAAS,CAACC,KAAK,CAAC,CAAC;AAEzG;AACA,OAAO,MAAMC,UAAU,GAAUC,MAA2B,IAC1DrD,IAAI,CAAC2C,GAAG,CAAC3C,IAAI,CAAC8B,IAAI,CAACuB,MAAM,CAAC,EAAExB,IAAI,CAAC;AAEnC;AACA,OAAO,MAAMT,EAAE,GAAUK,IAAuB,IAAsBA,IAAI,CAACL,EAAE,EAAE;AAE/E;AACA,OAAO,MAAMiB,UAAU,GAAUZ,IAAuB,IAA0BA,IAAI,CAACY,UAAU;AAEjG;AACA,OAAO,MAAMiB,WAAW,GAAIC,OAAwB,IAAyB1B,IAAI,CAACxC,IAAI,CAACmE,SAAS,CAACD,OAAO,CAAC,CAAC;AAE1G;AACA,OAAO,MAAME,YAAY,GAAIX,MAAuC,IAClE9C,IAAI,CAAC0D,OAAO,CAAC1D,IAAI,CAACuD,OAAO,EAAGA,OAAO,IAAK9D,IAAI,CAACqD,MAAM,EAAEa,cAAc,CAACJ,OAAO,CAAC,CAAC,CAAC;AAEhF;AACA,OAAO,MAAMI,cAAc,gBAAGnE,IAAI,CAGhC,CAAC,EAAE,CAACsD,MAAM,EAAES,OAAO,KACnB9D,IAAI,CACFO,IAAI,CAAC4D,wBAAwB,CAACd,MAAM,EAAEL,eAAe,CAACc,OAAO,CAAC,CAAC,EAC/DvD,IAAI,CAAC6D,QAAQ,CAACpE,IAAI,CAACqD,MAAM,EAAE9C,IAAI,CAAC4D,wBAAwB,CAAClC,MAAM,CAAC,CAAC,CAAC,CACnE,CAAC;AAEJ;AACA,OAAO,MAAMe,eAAe,gBAAGjD,IAAI,CAGjC,CAAC,EAAE,CAACiC,IAAI,EAAE8B,OAAO,KAAK9B,IAAI,CAACgB,eAAe,CAACc,OAAO,CAAC,CAAC;AAEtD;AACA,OAAO,MAAMrB,IAAI,GAAUT,IAAuB,IAChDzB,IAAI,CAAC8D,OAAO,CAAC9D,IAAI,CAAC+D,OAAO,CAACtC,IAAI,CAACE,KAAK,CAAC,EAAEF,IAAI,CAACY,UAAU,CAAC;AAEzD;AACA,OAAO,MAAMM,GAAG,gBAAGnD,IAAI,CAGrB,CAAC,EAAE,CAACiC,IAAI,EAAEuC,CAAC,KAAKC,SAAS,CAACxC,IAAI,EAAGyC,CAAC,IAAKlE,IAAI,CAACmE,IAAI,CAAC,MAAMH,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhE;AACA,OAAO,MAAMD,SAAS,gBAAGzE,IAAI,CAG3B,CAAC,EAAE,CAACiC,IAAI,EAAEuC,CAAC,KAAI;EACf,MAAMjC,MAAM,GAAG;IACb,GAAG9B,UAAU,CAAC+B,eAAe;IAC7BC,MAAMA,CAAA;MACJ,OAAOC,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,GAAGtB,UAAU;IACbQ,EAAE,EAAEA,CAAA,KAAMK,IAAI,CAACL,EAAE,EAAE;IACnBO,KAAK,EAAE3B,IAAI,CAAC0D,OAAO,CAACjC,IAAI,CAACE,KAAK,EAAEtC,IAAI,CAAC+E,aAAa,CAACJ,CAAC,CAAC,CAAC;IACtDpC,QAAQ,EAAEH,IAAI,CAACG,QAAQ;IACvBS,UAAU,EAAEZ,IAAI,CAACY,UAAU;IAC3BE,IAAI,EAAEvC,IAAI,CAAC0D,OAAO,CAACjC,IAAI,CAACc,IAAI,EAAG8B,MAAM,IAAI;MACvC,QAAQA,MAAM,CAACC,IAAI;QACjB,KAAK,MAAM;UACT,OAAOtE,IAAI,CAACoC,OAAO,CAACxC,MAAM,CAACuC,IAAI,EAAE,CAAC;QACpC,KAAK,MAAM;UACT,OAAO1C,IAAI,CACTJ,IAAI,CAAC+E,aAAa,CAACC,MAAM,CAACE,KAAK,EAAEP,CAAC,CAAC,EACnChE,IAAI,CAAC2C,GAAG,CAAC/C,MAAM,CAAC4C,IAAI,CAAC,CACtB;MACL;IACF,CAAC,CAAC;IACFC,eAAe,EAAGrB,EAAmB,IAAKK,IAAI,CAACgB,eAAe,CAACrB,EAAE;GAClE;EACD,OAAOW,MAAM;AACf,CAAC,CAAC;AAEF;AACA,OAAO,MAAMyC,QAAQ,gBAAGhF,IAAI,CAQ1B,CAAC,EAAE,CACHiC,IAAuB,EACvBuC,CAA+B,KAE/BhE,IAAI,CAAC2C,GAAG,CACNlB,IAAI,CAACE,KAAK,EACVtC,IAAI,CAACoF,KAAK,CAAC;EACTC,SAAS,EAAGvB,KAAK,IAA6BD,SAAS,CAACC,KAAK,CAAC;EAC9DwB,SAAS,EAAGT,CAAC,IAAKF,CAAC,CAACE,CAAC;CACtB,CAAC,CACH,CAAC;AAEJ;AACA,OAAO,MAAMO,KAAK,gBAAGjF,IAAI,CAcvB,CAAC,EAAE,CAACiC,IAAI,EAAE;EAAEmD,OAAO;EAAEC;AAAc,CAAE,KAAI;EACzC,IAAIrD,cAAc,CAACC,IAAI,CAAC,EAAE;IACxB,OAAOoD,cAAc,CAACpD,IAAI,CAAC;EAC7B;EACA,OAAOmD,OAAO,CAACnD,IAAI,CAAC;AACtB,CAAC,CAAC;AAEF;AACA,MAAMqD,MAAM,GAAG;EACb,GAAG7E,UAAU,CAAC+B,eAAe;EAC7BC,MAAMA,CAAA;IACJ,OAAOC,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,GAAGtB,UAAU;EACbQ,EAAE,EAAEA,CAAA,KAAM9B,OAAO,CAAC6C,IAAI;EACtBR,KAAK,EAAE3B,IAAI,CAAC+E,KAAK;EACjBnD,QAAQ,eAAE5B,IAAI,CAACoC,OAAO,CAAC,EAAE,CAAC;EAC1BC,UAAU,EAAErC,IAAI,CAAC+E,KAAK;EACtBxC,IAAI,eAAEvC,IAAI,CAACoC,OAAO,cAACxC,MAAM,CAACuC,IAAI,EAAE,CAAC;EACjCM,eAAe,EAAEA,CAAA,KAAMzC,IAAI,CAAC+E;CAC7B;AAED;AACA,OAAO,MAAMA,KAAK,GAAuBD,MAAM;AAE/C;AACA,OAAO,MAAME,MAAM,gBAAGxF,IAAI,CAGxB,CAAC,EAAE,CAACiC,IAAI,EAAEwD,IAAI,MAAM;EACpB,GAAGhF,UAAU,CAAC+B,eAAe;EAC7BC,MAAMA,CAAA;IACJ,OAAOC,IAAI,CAAC,IAAI,CAAC;EACnB,CAAC;EACD,GAAGtB,UAAU;EACbQ,EAAE,EAAEA,CAAA,KAAM9B,OAAO,CAAC4F,SAAS,CAACzD,IAAI,CAACL,EAAE,EAAE,EAAE6D,IAAI,CAAC7D,EAAE,EAAE,CAAC;EACjDO,KAAK,EAAE3B,IAAI,CAACmF,OAAO,CACjB1D,IAAI,CAACE,KAAK,EACVsD,IAAI,CAACtD,KAAK,EACV,CAACyD,KAAK,EAAEC,KAAK,KAAMhG,IAAI,CAACiG,SAAS,CAACF,KAAK,CAAC,GAAGA,KAAK,GAAGC,KAAM,CAC1D;EACDzD,QAAQ,EAAEH,IAAI,CAACG,QAAQ;EACvBS,UAAU,EAAErC,IAAI,CAAC6D,QAAQ,CAACoB,IAAI,CAAC5C,UAAU,EAAEZ,IAAI,CAACY,UAAU,CAAC;EAC3DE,IAAI,EAAEvC,IAAI,CAACmF,OAAO,CAChB1D,IAAI,CAACc,IAAI,EACT0C,IAAI,CAAC1C,IAAI,EACT,CAACgD,OAAO,EAAEC,OAAO,KAAI;IACnB,QAAQD,OAAO,CAACjB,IAAI;MAClB,KAAK,MAAM;QAAE;UACX,OAAO1E,MAAM,CAACuC,IAAI,EAAE;QACtB;MACA,KAAK,MAAM;QAAE;UACX,OAAO9C,IAAI,CAACiG,SAAS,CAACC,OAAO,CAAChB,KAAK,CAAC,GAAGgB,OAAO,GAAGC,OAAO;QAC1D;IACF;EACF,CAAC,CACF;EACD/C,eAAe,EAAGrB,EAAE,IAClB3B,IAAI,CACFO,IAAI,CAACyF,gBAAgB,CAAChE,IAAI,EAAEL,EAAE,CAAC,EAC/BpB,IAAI,CAAC6D,QAAQ,CAACpE,IAAI,CAACwF,IAAI,EAAEjF,IAAI,CAACyF,gBAAgB,CAACrE,EAAE,CAAC,CAAC,CAAC,EACpDpB,IAAI,CAAC0F,MAAM;CAEhB,CAAC,CAAC;AAEH;AACA,OAAO,MAAMC,YAAY,gBAAGnG,IAAI,CAG9B,CAAC,EAAE,CAACiC,IAAI,EAAEwD,IAAI,KAAKD,MAAM,CAACrC,GAAG,CAAClB,IAAI,EAAErC,MAAM,CAACwG,IAAI,CAAC,EAAEjD,GAAG,CAACsC,IAAI,EAAE7F,MAAM,CAACyG,KAAK,CAAC,CAAC,CAAC;AAE7E;AACA,OAAO,MAAMtD,IAAI,GAAUd,IAAuB,IAAoDA,IAAI,CAACc,IAAI;AAE/G;AACA;AACA;AACA;AACA,MAAMuD,OAAO,GAAIC,YAAoB,IAAI;EACvC,MAAMC,gBAAgB,GAAGD,YAAY,GAAG,CAAC,GAAGE,IAAI,CAACC,KAAK,GAAGD,IAAI,CAACE,IAAI;EAClE,OAAO;IACLC,IAAI,EAAEJ,gBAAgB,CAACD,YAAY,GAAG,QAAQ,CAAC;IAC/CM,KAAK,EAAEL,gBAAgB,CAACD,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;IACpDO,OAAO,EAAEN,gBAAgB,CAACD,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE;IACpDQ,OAAO,EAAEP,gBAAgB,CAACD,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE;IACnDA,YAAY,EAAEC,gBAAgB,CAACD,YAAY,CAAC,GAAG,IAAI;IACnDS,YAAY,EAAER,gBAAgB,CAACD,YAAY,GAAG,IAAI,CAAC,GAAG,IAAI;IAC1DU,WAAW,EAAET,gBAAgB,CAACD,YAAY,GAAG,GAAG,CAAC,GAAG;GACrD;AACH,CAAC;AAED;AACA,MAAMW,YAAY,GAAI9D,MAA+B,IAAY;EAC/D,IAAIrD,WAAW,CAACoH,MAAM,CAAC/D,MAAM,CAAC,EAAE;IAC9B,OAAO,MAAM;EACf;EACA,IAAIrD,WAAW,CAACqH,SAAS,CAAChE,MAAM,CAAC,EAAE;IACjC,OAAO,SAAS;EAClB;EAEA,MAAMiE,eAAe,GAAG1G,YAAY,CAAC2G,aAAa,CAAClE,MAAM,CAACzC,YAAY,CAAC,GACrE,eAAe,GACf,iBAAiB;EACnB,OAAO,aAAa0G,eAAe,GAAG;AACxC,CAAC;AAED;AACA,OAAO,MAAME,MAAM,GAAUtF,IAA8B,IACzDzB,IAAI,CAAC0D,OAAO,CAACvE,KAAK,CAAC6H,iBAAiB,EAAGC,GAAG,IACxCjH,IAAI,CAAC2C,GAAG,CAACD,IAAI,CAACjB,IAAI,CAAC,EAAGiB,IAAI,IAAI;EAC5B,MAAMwE,IAAI,GAAGD,GAAG,GAAGvE,IAAI,CAACtB,EAAE,CAACC,eAAe;EAC1C,MAAM;IAAE+E,IAAI;IAAEC,KAAK;IAAEN,YAAY;IAAEO,OAAO;IAAEC;EAAO,CAAE,GAAGT,OAAO,CAACoB,IAAI,CAAC;EACrE,MAAMC,OAAO,GAAG,CAACf,IAAI,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,IAAI,GAAG,KAC1CA,IAAI,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,KAAK,GAAG,CAAC,IAC7CD,IAAI,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,OAAO,GAAG,CAAC,IAChEF,IAAI,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,GAAG,EAAE,GAAG,GAAGA,OAAO,GAAG,CAAC,GAClF,GAAGR,YAAY,IAAI;EACrB,MAAMqB,OAAO,GAAG7H,WAAW,CAAC8H,WAAW,CAAC3E,IAAI,CAACE,MAAM,CAAC,GAClD,CAAC,MAAK;IACJ,MAAM0E,GAAG,GAAGhI,OAAO,CAACgI,GAAG,CAAC5E,IAAI,CAACE,MAAM,CAAC2E,UAAU,CAAC;IAC/C,OAAO7H,OAAO,CAAC8H,IAAI,CAACF,GAAG,CAAC,GAAG,CAAC,GACxB,aAAa,GAAGG,KAAK,CAACC,IAAI,CAACJ,GAAG,CAAC,CAAC3E,GAAG,CAAEvB,EAAE,IAAK,GAAGA,EAAE,EAAE,CAAC,CAACc,IAAI,CAAC,IAAI,CAAC,GAC/D,EAAE;EACR,CAAC,EAAC,CAAE,GACJ,EAAE;EACJ,MAAMyF,SAAS,GAAGjB,YAAY,CAAChE,IAAI,CAACE,MAAM,CAAC;EAC3C,OAAO,YAAYF,IAAI,CAACtB,EAAE,CAACA,EAAE,MAAM+F,OAAO,KAAKC,OAAO,gBAAgBO,SAAS,EAAE;AACnF,CAAC,CAAC,CAAC;AAEP;AACA,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAA2CH,KAAK,CAACC,IAAI,CAACxH,UAAU,CAAC2H,WAAW,CAACC,KAAK,CAAC;AAE9G;AACA,OAAO,MAAMA,KAAK,gBAAuD9H,IAAI,CAACmE,IAAI,CAACyD,WAAW,CAAC;AAE/F;AACA,OAAO,MAAMhF,MAAM,GAAUnB,IAA8B,IAA6CA,IAAI,CAACmB,MAAM;AAEnH;AACA,OAAO,MAAMR,OAAO,GAAOmC,KAAQ,IAAqB1C,IAAI,CAACxC,IAAI,CAAC+C,OAAO,CAACmC,KAAK,CAAC,CAAC;AAEjF,MAAMwD,KAAK,gBAAsB3F,OAAO,CAAC,KAAK,CAAC,CAAC;AAChD,SACE;AACA2F,KAAK,IAAIzF,IAAI;AAGf;AACA,OAAO,MAAM0F,eAAe,GAAG,qBAAqB;AAEpD;AACA,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAC7BrI,MAAM,CAACsI,YAAY,CAAEC,UAAkB,CAACH,eAAe,CAAC,CAAC", "ignoreList": []}