{"version": 3, "file": "data.js", "names": ["Equal", "Hash", "StructuralPrototype", "ArrayProto", "Object", "assign", "create", "Array", "prototype", "symbol", "cached", "array", "that", "isArray", "length", "every", "v", "i", "equals", "Structural", "args", "struct", "as"], "sources": ["../../../src/internal/data.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,aAAa;AACpC,OAAO,KAAKC,IAAI,MAAM,YAAY;AAElC,SAASC,mBAAmB,QAAQ,iBAAiB;AAErD;AACA,OAAO,MAAMC,UAAU,gBAAgBC,MAAM,CAACC,MAAM,cAACD,MAAM,CAACE,MAAM,CAACC,KAAK,CAACC,SAAS,CAAC,EAAE;EACnF,CAACP,IAAI,CAACQ,MAAM,IAAC;IACX,OAAOR,IAAI,CAACS,MAAM,CAAC,IAAI,EAAET,IAAI,CAACU,KAAK,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC;EACD,CAACX,KAAK,CAACS,MAAM,EAAoBG,IAAiB;IAChD,IAAIL,KAAK,CAACM,OAAO,CAACD,IAAI,CAAC,IAAI,IAAI,CAACE,MAAM,KAAKF,IAAI,CAACE,MAAM,EAAE;MACtD,OAAO,IAAI,CAACC,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKjB,KAAK,CAACkB,MAAM,CAACF,CAAC,EAAGJ,IAAmB,CAACK,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,MAAM;MACL,OAAO,KAAK;IACd;EACF;CACD,CAAC;AAEF;AACA,OAAO,MAAME,UAAU,gBAGZ;EACT,SAASA,UAAUA,CAAYC,IAAS;IACtC,IAAIA,IAAI,EAAE;MACRhB,MAAM,CAACC,MAAM,CAAC,IAAI,EAAEe,IAAI,CAAC;IAC3B;EACF;EACAD,UAAU,CAACX,SAAS,GAAGN,mBAAmB;EAC1C,OAAOiB,UAAiB;AAC1B,CAAC,CAAC,CAAE;AAEJ;AACA,OAAO,MAAME,MAAM,GAA8CC,EAAM,IACrElB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACE,MAAM,CAACJ,mBAAmB,CAAC,EAAEoB,EAAE,CAAC", "ignoreList": []}