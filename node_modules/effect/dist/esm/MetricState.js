import * as internal from "./internal/metric/state.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const MetricStateTypeId = internal.MetricStateTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const CounterStateTypeId = internal.CounterStateTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const FrequencyStateTypeId = internal.FrequencyStateTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const GaugeStateTypeId = internal.GaugeStateTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const HistogramStateTypeId = internal.HistogramStateTypeId;
/**
 * @since 2.0.0
 * @category symbols
 */
export const SummaryStateTypeId = internal.SummaryStateTypeId;
/**
 * @since 2.0.0
 * @category constructors
 */
export const counter = internal.counter;
/**
 * @since 2.0.0
 * @category constructors
 */
export const frequency = internal.frequency;
/**
 * @since 2.0.0
 * @category constructors
 */
export const gauge = internal.gauge;
/**
 * @since 2.0.0
 * @category constructors
 */
export const histogram = internal.histogram;
/**
 * @since 2.0.0
 * @category constructors
 */
export const summary = internal.summary;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isMetricState = internal.isMetricState;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isCounterState = internal.isCounterState;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isFrequencyState = internal.isFrequencyState;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isGaugeState = internal.isGaugeState;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isHistogramState = internal.isHistogramState;
/**
 * @since 2.0.0
 * @category refinements
 */
export const isSummaryState = internal.isSummaryState;
//# sourceMappingURL=MetricState.js.map