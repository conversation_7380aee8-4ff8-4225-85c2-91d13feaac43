{"version": 3, "file": "FiberSet.js", "names": ["Cause", "Deferred", "Effect", "Exit", "Fiber", "FiberId", "constFalse", "constVoid", "dual", "HashSet", "Inspectable", "Iterable", "pipeArguments", "Predicate", "Runtime", "TypeId", "Symbol", "for", "isFiberSet", "u", "hasProperty", "Proto", "iterator", "state", "_tag", "empty", "backing", "toString", "format", "toJSON", "_id", "NodeInspectSymbol", "pipe", "arguments", "unsafeMake", "deferred", "self", "Object", "create", "make", "acquireRelease", "map", "Set", "set", "withFiberRuntime", "parent", "void", "fibers", "interruptAllAs", "combine", "id", "internalFiberId", "into<PERSON><PERSON><PERSON><PERSON>", "makeRuntime", "flatMap", "runtime", "makeRuntimePromise", "runtimePromise", "internalFiberIdId", "isInternalInterruption", "reduceWithContext", "undefined", "emptyCase", "failCase", "dieCase", "interruptCase", "_", "fiberId", "has", "ids", "sequentialCase", "left", "right", "parallelCase", "unsafeAdd", "args", "fiber", "options", "unsafeInterruptAsFork", "interruptAs", "none", "add", "addObserver", "exit", "delete", "isFailure", "propagateInterruption", "cause", "isInterruptedOnly", "unsafeDone", "fiberIdWith", "sync", "clear", "clearFiber", "for<PERSON>ach", "constInterruptedFiber", "runFork", "interrupt", "run", "isEffect", "effect", "runImpl", "tap", "forkDaemon", "Promise", "resolve", "reject", "isSuccess", "value", "squash", "size", "join", "await", "await<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "while", "body", "unsafeHead", "step"], "sources": ["../../src/FiberSet.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,QAAQ,MAAM,eAAe;AACzC,OAAO,KAAKC,MAAM,MAAM,aAAa;AACrC,OAAO,KAAKC,IAAI,MAAM,WAAW;AACjC,OAAO,KAAKC,KAAK,MAAM,YAAY;AACnC,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,SAASC,UAAU,EAAEC,SAAS,EAAEC,IAAI,QAAQ,eAAe;AAC3D,OAAO,KAAKC,OAAO,MAAM,cAAc;AACvC,OAAO,KAAKC,WAAW,MAAM,kBAAkB;AAC/C,OAAO,KAAKC,QAAQ,MAAM,eAAe;AACzC,SAAwBC,aAAa,QAAQ,eAAe;AAC5D,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAC3C,OAAO,KAAKC,OAAO,MAAM,cAAc;AAGvC;;;;AAIA,OAAO,MAAMC,MAAM,gBAAkBC,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC;AA0BlE;;;;AAIA,OAAO,MAAMC,UAAU,GAAIC,CAAU,IAAsCN,SAAS,CAACO,WAAW,CAACD,CAAC,EAAEJ,MAAM,CAAC;AAE3G,MAAMM,KAAK,GAAG;EACZ,CAACN,MAAM,GAAGA,MAAM;EAChB,CAACC,MAAM,CAACM,QAAQ,IAAC;IACf,IAAI,IAAI,CAACC,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAChC,OAAOb,QAAQ,CAACc,KAAK,EAAE;IACzB;IACA,OAAO,IAAI,CAACF,KAAK,CAACG,OAAO,CAACV,MAAM,CAACM,QAAQ,CAAC,EAAE;EAC9C,CAAC;EACDK,QAAQA,CAAA;IACN,OAAOjB,WAAW,CAACkB,MAAM,CAAC,IAAI,CAACC,MAAM,EAAE,CAAC;EAC1C,CAAC;EACDA,MAAMA,CAAA;IACJ,OAAO;MACLC,GAAG,EAAE,UAAU;MACfP,KAAK,EAAE,IAAI,CAACA;KACb;EACH,CAAC;EACD,CAACb,WAAW,CAACqB,iBAAiB,IAAC;IAC7B,OAAO,IAAI,CAACF,MAAM,EAAE;EACtB,CAAC;EACDG,IAAIA,CAAA;IACF,OAAOpB,aAAa,CAAC,IAAI,EAAEqB,SAAS,CAAC;EACvC;CACD;AAED,MAAMC,UAAU,GAAGA,CACjBR,OAAsC,EACtCS,QAA0C,KACxB;EAClB,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAACjB,KAAK,CAAC;EACjCe,IAAI,CAACb,KAAK,GAAG;IAAEC,IAAI,EAAE,MAAM;IAAEE;EAAO,CAAE;EACtCU,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EACxB,OAAOC,IAAI;AACb,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAO,MAAMG,IAAI,GAAGA,CAAA,KAClBrC,MAAM,CAACsC,cAAc,CACnBtC,MAAM,CAACuC,GAAG,CAACxC,QAAQ,CAACsC,IAAI,EAAiB,EAAGJ,QAAQ,IAAKD,UAAU,CAAC,IAAIQ,GAAG,EAAE,EAAEP,QAAQ,CAAC,CAAC,EACxFQ,GAAG,IACFzC,MAAM,CAAC0C,gBAAgB,CAAEC,MAAM,IAAI;EACjC,MAAMtB,KAAK,GAAGoB,GAAG,CAACpB,KAAK;EACvB,IAAIA,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE,OAAOtB,MAAM,CAAC4C,IAAI;EAC/CH,GAAG,CAACpB,KAAK,GAAG;IAAEC,IAAI,EAAE;EAAQ,CAAE;EAC9B,MAAMuB,MAAM,GAAGxB,KAAK,CAACG,OAAO;EAC5B,OAAOtB,KAAK,CAAC4C,cAAc,CAACD,MAAM,EAAE1C,OAAO,CAAC4C,OAAO,CAACJ,MAAM,CAACK,EAAE,EAAE,EAAEC,eAAe,CAAC,CAAC,CAACnB,IAAI,CACrF9B,MAAM,CAACkD,YAAY,CAACT,GAAG,CAACR,QAAQ,CAAC,CAClC;AACH,CAAC,CAAC,CACL;AAEH;;;;;;AAMA,OAAO,MAAMkB,WAAW,GAAGA,CAAA,KAQzBnD,MAAM,CAACoD,OAAO,CACZf,IAAI,EAAQ,EACXH,IAAI,IAAKmB,OAAO,CAACnB,IAAI,CAAC,EAAK,CAC7B;AAEH;;;;;;AAMA,OAAO,MAAMoB,kBAAkB,GAAGA,CAAA,KAQhCtD,MAAM,CAACoD,OAAO,CACZf,IAAI,EAAQ,EACXH,IAAI,IAAKqB,cAAc,CAACrB,IAAI,CAAC,EAAK,CACpC;AAEH,MAAMsB,iBAAiB,GAAG,CAAC,CAAC;AAC5B,MAAMP,eAAe,gBAAG9C,OAAO,CAACkC,IAAI,CAACmB,iBAAiB,EAAE,CAAC,CAAC;AAC1D,MAAMC,sBAAsB,gBAAG3D,KAAK,CAAC4D,iBAAiB,CAACC,SAAS,EAAE;EAChEC,SAAS,EAAExD,UAAU;EACrByD,QAAQ,EAAEzD,UAAU;EACpB0D,OAAO,EAAE1D,UAAU;EACnB2D,aAAa,EAAEA,CAACC,CAAC,EAAEC,OAAO,KAAK1D,OAAO,CAAC2D,GAAG,CAAC/D,OAAO,CAACgE,GAAG,CAACF,OAAO,CAAC,EAAET,iBAAiB,CAAC;EACnFY,cAAc,EAAEA,CAACJ,CAAC,EAAEK,IAAI,EAAEC,KAAK,KAAKD,IAAI,IAAIC,KAAK;EACjDC,YAAY,EAAEA,CAACP,CAAC,EAAEK,IAAI,EAAEC,KAAK,KAAKD,IAAI,IAAIC;CAC3C,CAAC;AAEF;;;;;;AAMA,OAAO,MAAME,SAAS,gBA4BlBlE,IAAI,CAAEmE,IAAI,IAAKzD,UAAU,CAACyD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CACtCvC,IAAoB,EACpBwC,KAAiC,EACjCC,OAGa,KACL;EACR,IAAIzC,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;IAChCoD,KAAK,CAACE,qBAAqB,CAACzE,OAAO,CAAC4C,OAAO,CAAC4B,OAAO,EAAEE,WAAW,IAAI1E,OAAO,CAAC2E,IAAI,EAAE7B,eAAe,CAAC,CAAC;IACnG;EACF,CAAC,MAAM,IAAIf,IAAI,CAACb,KAAK,CAACG,OAAO,CAAC0C,GAAG,CAACQ,KAAK,CAAC,EAAE;IACxC;EACF;EACAxC,IAAI,CAACb,KAAK,CAACG,OAAO,CAACuD,GAAG,CAACL,KAAK,CAAC;EAC7BA,KAAK,CAACM,WAAW,CAAEC,IAAI,IAAI;IACzB,IAAI/C,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAChC;IACF;IACAY,IAAI,CAACb,KAAK,CAACG,OAAO,CAAC0D,MAAM,CAACR,KAAK,CAAC;IAChC,IACEzE,IAAI,CAACkF,SAAS,CAACF,IAAI,CAAC,KAElBN,OAAO,EAAES,qBAAqB,KAAK,IAAI,GACrC,CAAC3B,sBAAsB,CAACwB,IAAI,CAACI,KAAK,CAAC,GACnC,CAACvF,KAAK,CAACwF,iBAAiB,CAACL,IAAI,CAACI,KAAK,CAAC,CACvC,EACD;MACAtF,QAAQ,CAACwF,UAAU,CAACrD,IAAI,CAACD,QAAQ,EAAEgD,IAAW,CAAC;IACjD;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;AAMA,OAAO,MAAMF,GAAG,gBA0BZzE,IAAI,CACLmE,IAAI,IAAKzD,UAAU,CAACyD,IAAI,CAAC,CAAC,CAAC,CAAC,EAC7B,CACEvC,IAAoB,EACpBwC,KAAiC,EACjCC,OAEa,KAEb3E,MAAM,CAACwF,WAAW,CAAEvB,OAAO,IACzBjE,MAAM,CAACyF,IAAI,CAAC,MACVjB,SAAS,CAACtC,IAAI,EAAEwC,KAAK,EAAE;EACrB,GAAGC,OAAO;EACVE,WAAW,EAAEZ;CACd,CAAC,CACH,CACF,CACJ;AAED;;;;AAIA,OAAO,MAAMyB,KAAK,GAAUxD,IAAoB,IAC9ClC,MAAM,CAAC0C,gBAAgB,CAAEiD,UAAU,IAAI;EACrC,IAAIzD,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;IAChC,OAAOtB,MAAM,CAAC4C,IAAI;EACpB;EACA,OAAO5C,MAAM,CAAC4F,OAAO,CAAC1D,IAAI,CAACb,KAAK,CAACG,OAAO,EAAGkD,KAAK;EAC9C;EACAxE,KAAK,CAAC2E,WAAW,CAACH,KAAK,EAAEvE,OAAO,CAAC4C,OAAO,CAAC4C,UAAU,CAAC3C,EAAE,EAAE,EAAEC,eAAe,CAAC,CAAC,CAAC;AAChF,CAAC,CAAC;AAEJ,MAAM4C,qBAAqB,gBAAI;EAC7B,IAAInB,KAAK,GAAiDf,SAAS;EACnE,OAAO,MAAK;IACV,IAAIe,KAAK,KAAKf,SAAS,EAAE;MACvBe,KAAK,GAAG1E,MAAM,CAAC8F,OAAO,CAAC9F,MAAM,CAAC+F,SAAS,CAAC;IAC1C;IACA,OAAOrB,KAAK;EACd,CAAC;AACH,CAAC,CAAC,CAAE;AAEJ;;;;;;;AAOA,OAAO,MAAMsB,GAAG,GA8BZ,SAAAA,CAAA;EACF,MAAM9D,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAuB;EAC/C,IAAI,CAAC/B,MAAM,CAACiG,QAAQ,CAAClE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;IAClC,MAAM4C,OAAO,GAAG5C,SAAS,CAAC,CAAC,CAAC;IAC5B,OAAQmE,MAAoC,IAAKC,OAAO,CAACjE,IAAI,EAAEgE,MAAM,EAAEvB,OAAO,CAAC;EACjF;EACA,OAAOwB,OAAO,CAACjE,IAAI,EAAEH,SAAS,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAQ;AACzD,CAAC;AAED,MAAMoE,OAAO,GAAGA,CACdjE,IAAoB,EACpBgE,MAAgC,EAChCvB,OAEC,KAED3E,MAAM,CAACwF,WAAW,CAAEvB,OAAO,IAAI;EAC7B,IAAI/B,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;IAChC,OAAOtB,MAAM,CAACyF,IAAI,CAACI,qBAAqB,CAAC;EAC3C;EACA,OAAO7F,MAAM,CAACoG,GAAG,CACfpG,MAAM,CAACqG,UAAU,CAACH,MAAM,CAAC,EACxBxB,KAAK,IACJF,SAAS,CAACtC,IAAI,EAAEwC,KAAK,EAAE;IACrB,GAAGC,OAAO;IACVE,WAAW,EAAEZ;GACd,CAAC,CACL;AACH,CAAC,CAAC;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,OAAO,MAAMZ,OAAO,GAWTnB,IAAoB,IAAK,MAClClC,MAAM,CAACuC,GAAG,CACRvC,MAAM,CAACqD,OAAO,EAAK,EAClBA,OAAO,IAAI;EACV,MAAMyC,OAAO,GAAGlF,OAAO,CAACkF,OAAO,CAACzC,OAAO,CAAC;EACxC,OAAO,CACL6C,MAAgC,EAChCvB,OAEa,KACX;IACF,IAAIzC,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,EAAE;MAChC,OAAOuE,qBAAqB,EAAE;IAChC;IACA,MAAMnB,KAAK,GAAGoB,OAAO,CAACI,MAAM,EAAEvB,OAAO,CAAC;IACtCH,SAAS,CAACtC,IAAI,EAAEwC,KAAK,CAAC;IACtB,OAAOA,KAAK;EACd,CAAC;AACH,CAAC,CACF;AAEH;;;;;;;;AAQA,OAAO,MAAMnB,cAAc,GAAUrB,IAAoB,IAUzD,MACElC,MAAM,CAACuC,GAAG,CACRc,OAAO,CAACnB,IAAI,CAAC,EAAK,EACjB4D,OAAO,IACR,CACEI,MAAgC,EAChCvB,OAEa,KAEb,IAAI2B,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAC1BV,OAAO,CAACI,MAAM,EAAEvB,OAAO,CAAC,CAACK,WAAW,CAAEC,IAAI,IAAI;EAC5C,IAAIhF,IAAI,CAACwG,SAAS,CAACxB,IAAI,CAAC,EAAE;IACxBsB,OAAO,CAACtB,IAAI,CAACyB,KAAK,CAAC;EACrB,CAAC,MAAM;IACLF,MAAM,CAAC1G,KAAK,CAAC6G,MAAM,CAAC1B,IAAI,CAACI,KAAK,CAAC,CAAC;EAClC;AACF,CAAC,CAAC,CACH,CACJ;AAEH;;;;AAIA,OAAO,MAAMuB,IAAI,GAAU1E,IAAoB,IAC7ClC,MAAM,CAACyF,IAAI,CAAC,MAAMvD,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,QAAQ,GAAG,CAAC,GAAGY,IAAI,CAACb,KAAK,CAACG,OAAO,CAACoF,IAAI,CAAC;AAE/E;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMC,IAAI,GAAU3E,IAAoB,IAC7CnC,QAAQ,CAAC+G,KAAK,CAAC5E,IAAI,CAACD,QAAsC,CAAC;AAE7D;;;;;;AAMA,OAAO,MAAM8E,UAAU,GAAU7E,IAAoB,IACnDlC,MAAM,CAACgH,SAAS,CAAC;EACfC,KAAK,EAAEA,CAAA,KAAM/E,IAAI,CAACb,KAAK,CAACC,IAAI,KAAK,MAAM,IAAIY,IAAI,CAACb,KAAK,CAACG,OAAO,CAACoF,IAAI,GAAG,CAAC;EACtEM,IAAI,EAAEA,CAAA,KAAMhH,KAAK,CAAC4G,KAAK,CAACrG,QAAQ,CAAC0G,UAAU,CAACjF,IAAI,CAAC,CAAC;EAClDkF,IAAI,EAAE/G;CACP,CAAC", "ignoreList": []}