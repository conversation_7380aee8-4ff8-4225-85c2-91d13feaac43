{"version": 3, "file": "Data.js", "names": ["core", "internal", "StructuralPrototype", "Predicate", "struct", "unsafeStruct", "as", "Object", "setPrototypeOf", "tuple", "unsafeArray", "array", "slice", "ArrayProto", "_case", "args", "undefined", "create", "case", "tagged", "tag", "value", "_tag", "Class", "Structural", "TaggedClass", "Base", "taggedEnum", "Proxy", "get", "_target", "_receiver", "isTagged", "taggedMatch", "arguments", "length", "cases", "Error", "plainArgsSymbol", "Symbol", "for", "O", "BaseEffectError", "YieldableError", "constructor", "message", "cause", "assign", "defineProperty", "enumerable", "toJSON", "TaggedError", "prototype", "name"], "sources": ["../../src/Data.ts"], "sourcesContent": [null], "mappings": "AAIA,OAAO,KAAKA,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,QAAQ,MAAM,oBAAoB;AAC9C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,KAAKC,SAAS,MAAM,gBAAgB;AAoB3C;;;;;;;;;;;;;;;;;;;;AAoBA,OAAO,MAAMC,MAAM,GAA+EH,QAAQ,CAACG,MAAM;AAEjH;;;;AAIA,OAAO,MAAMC,YAAY,GAAmCC,EAAK,IAC/DC,MAAM,CAACC,cAAc,CAACF,EAAE,EAAEJ,mBAAmB,CAAC;AAEhD;;;;;;;;;;;;;;;;;;;;AAoBA,OAAO,MAAMO,KAAK,GAAGA,CAAgC,GAAGH,EAAM,KAAmBI,WAAW,CAACJ,EAAE,CAAC;AAEhG;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,OAAO,MAAMK,KAAK,GAAmCL,EAAM,IAAmBI,WAAW,CAACJ,EAAE,CAACM,KAAK,CAAC,CAAC,CAAkB,CAAC;AAEvH;;;;AAIA,OAAO,MAAMF,WAAW,GAAmCJ,EAAM,IAC/DC,MAAM,CAACC,cAAc,CAACF,EAAE,EAAEL,QAAQ,CAACY,UAAU,CAAC;AAEhD,MAAMC,KAAK,GAAGA,CAAA,KAA+BC,IAAI,IAC9CA,IAAI,KAAKC,SAAS,GAAGT,MAAM,CAACU,MAAM,CAACf,mBAAmB,CAAC,GAAGE,MAAM,CAACW,IAAI,CAAS;AAEjF;AACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BAD,KAAK,IAAII,IAAI;AAGf;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,MAAM,GACjBC,GAAc,IAEfL,IAAI,IAAI;EACP,MAAMM,KAAK,GAAGN,IAAI,KAAKC,SAAS,GAAGT,MAAM,CAACU,MAAM,CAACf,mBAAmB,CAAC,GAAGE,MAAM,CAACW,IAAI,CAAC;EACpFM,KAAK,CAACC,IAAI,GAAGF,GAAG;EAChB,OAAOC,KAAK;AACd,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAME,KAAK,GAGCtB,QAAQ,CAACuB,UAAiB;AAE7C;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,WAAW,GACtBL,GAAQ,IAIkC;EAC1C,MAAMM,IAAK,SAAQH,KAAU;IAClBD,IAAI,GAAGF,GAAG;;EAErB,OAAOM,IAAW;AACpB,CAAC;AAED;;;;AAIA,OAAO,MAAMF,UAAU,GAGbvB,QAAQ,CAACuB,UAAiB;AA6KpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA,OAAO,MAAMG,UAAU,GAiOnBA,CAAA,KACF,IAAIC,KAAK,CAAC,EAAE,EAAE;EACZC,GAAGA,CAACC,OAAO,EAAEV,GAAG,EAAEW,SAAS;IACzB,IAAIX,GAAG,KAAK,KAAK,EAAE;MACjB,OAAOjB,SAAS,CAAC6B,QAAQ;IAC3B,CAAC,MAAM,IAAIZ,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOa,WAAW;IACpB;IACA,OAAOd,MAAM,CAACC,GAAa,CAAC;EAC9B;CACD,CAAQ;AAcX,SAASa,WAAWA,CAAA;EAMlB,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,MAAMC,KAAK,GAAGF,SAAS,CAAC,CAAC,CAAU;IACnC,OAAO,UAASb,KAAQ;MACtB,OAAOe,KAAK,CAACf,KAAK,CAACC,IAAiB,CAAC,CAACD,KAAY,CAAC;IACrD,CAAC;EACH;EACA,MAAMA,KAAK,GAAGa,SAAS,CAAC,CAAC,CAAM;EAC/B,MAAME,KAAK,GAAGF,SAAS,CAAC,CAAC,CAAU;EACnC,OAAOE,KAAK,CAACf,KAAK,CAACC,IAAiB,CAAC,CAACD,KAAY,CAAC;AACrD;AAEA;;;;;;AAMA,OAAO,MAAMgB,KAAK,gBAGyB;EACzC,MAAMC,eAAe,gBAAGC,MAAM,CAACC,GAAG,CAAC,6BAA6B,CAAC;EACjE,MAAMC,CAAC,GAAG;IACRC,eAAe,EAAE,cAAc1C,IAAI,CAAC2C,cAAc;MAChDC,YAAY7B,IAAS;QACnB,KAAK,CAACA,IAAI,EAAE8B,OAAO,EAAE9B,IAAI,EAAE+B,KAAK,GAAG;UAAEA,KAAK,EAAE/B,IAAI,CAAC+B;QAAK,CAAE,GAAG9B,SAAS,CAAC;QACrE,IAAID,IAAI,EAAE;UACRR,MAAM,CAACwC,MAAM,CAAC,IAAI,EAAEhC,IAAI,CAAC;UACzB;UACAR,MAAM,CAACyC,cAAc,CAAC,IAAI,EAAEV,eAAe,EAAE;YAAEjB,KAAK,EAAEN,IAAI;YAAEkC,UAAU,EAAE;UAAK,CAAE,CAAC;QAClF;MACF;MACAC,MAAMA,CAAA;QACJ,OAAO;UAAE,GAAI,IAAY,CAACZ,eAAe,CAAC;UAAE,GAAG;QAAI,CAAE;MACvD;;GAEH;EACD,OAAOG,CAAC,CAACC,eAAe;AAC1B,CAAC,CAAC,CAAE;AAEJ;;;;AAIA,OAAO,MAAMS,WAAW,GAAwB/B,GAAQ,IAGW;EACjE,MAAMqB,CAAC,GAAG;IACRC,eAAe,EAAE,cAAcL,KAAS;MAC7Bf,IAAI,GAAGF,GAAG;;GAEtB;EACCqB,CAAC,CAACC,eAAe,CAACU,SAAiB,CAACC,IAAI,GAAGjC,GAAG;EAChD,OAAOqB,CAAC,CAACC,eAAsB;AACjC,CAAC", "ignoreList": []}