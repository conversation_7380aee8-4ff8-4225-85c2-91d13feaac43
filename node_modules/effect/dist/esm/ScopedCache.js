import * as internal from "./internal/scopedCache.js";
/**
 * @since 2.0.0
 * @category symbols
 */
export const ScopedCacheTypeId = internal.ScopedCacheTypeId;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
export const make = internal.make;
/**
 * Constructs a new cache with the specified capacity, time to live, and
 * lookup function, where the time to live can depend on the `Exit` value
 * returned by the lookup function.
 *
 * @since 2.0.0
 * @category constructors
 */
export const makeWith = internal.makeWith;
//# sourceMappingURL=ScopedCache.js.map