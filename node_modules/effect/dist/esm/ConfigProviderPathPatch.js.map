{"version": 3, "file": "ConfigProviderPathPatch.js", "names": ["internal", "empty", "and<PERSON><PERSON>", "mapName", "nested", "unnested"], "sources": ["../../src/ConfigProviderPathPatch.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAO,KAAKA,QAAQ,MAAM,wCAAwC;AAwDlE;;;;AAIA,OAAO,MAAMC,KAAK,GAAcD,QAAQ,CAACC,KAAK;AAE9C;;;;AAIA,OAAO,MAAMC,OAAO,GAWhBF,QAAQ,CAACE,OAAO;AAEpB;;;;AAIA,OAAO,MAAMC,OAAO,GAWhBH,QAAQ,CAACG,OAAO;AAEpB;;;;AAIA,OAAO,MAAMC,MAAM,GAWfJ,QAAQ,CAACI,MAAM;AAEnB;;;;AAIA,OAAO,MAAMC,QAAQ,GAWjBL,QAAQ,CAACK,QAAQ", "ignoreList": []}