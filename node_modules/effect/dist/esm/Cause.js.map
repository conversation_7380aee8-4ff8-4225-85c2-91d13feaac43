{"version": 3, "file": "Cause.js", "names": ["internal", "core", "CauseTypeId", "RuntimeExceptionTypeId", "InterruptedExceptionTypeId", "IllegalArgumentExceptionTypeId", "NoSuchElementExceptionTypeId", "InvalidPubSubCapacityExceptionTypeId", "ExceededCapacityExceptionTypeId", "TimeoutExceptionTypeId", "UnknownExceptionTypeId", "YieldableError", "empty", "fail", "die", "interrupt", "parallel", "sequential", "isCause", "isEmptyType", "isFailType", "isDieType", "isInterruptType", "isSequentialType", "isParallelType", "size", "isEmpty", "isFailure", "isDie", "isInterrupted", "isInterruptedOnly", "failures", "defects", "interruptors", "failureOption", "failureOrCause", "flipCauseOption", "dieOption", "interruptOption", "keepDefects", "linearize", "stripFailures", "stripSomeDefects", "as", "map", "flatMap", "and<PERSON><PERSON>", "flatten", "contains", "squash", "causeSquash", "squashWith", "causeSquashWith", "find", "filter", "match", "reduce", "reduceWithContext", "InterruptedException", "isInterruptedException", "IllegalArgumentException", "isIllegalArgumentException", "NoSuchElementException", "isNoSuchElementException", "RuntimeException", "isRuntimeException", "TimeoutException", "isTimeoutException", "UnknownException", "isUnknownException", "ExceededCapacityException", "isExceededCapacityException", "pretty", "prettyErrors", "originalError", "originalInstance"], "sources": ["../../src/Cause.ts"], "sourcesContent": [null], "mappings": "AA+BA,OAAO,KAAKA,QAAQ,MAAM,qBAAqB;AAC/C,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAS1C;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,WAAW,GAAkBF,QAAQ,CAACE,WAAW;AAQ9D;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,sBAAsB,GAAkBF,IAAI,CAACE,sBAAsB;AAQhF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,0BAA0B,GAAkBH,IAAI,CAACG,0BAA0B;AAQxF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,8BAA8B,GAAkBJ,IAAI,CAACI,8BAA8B;AAQhG;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,4BAA4B,GAAkBL,IAAI,CAACK,4BAA4B;AAQ5F;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,oCAAoC,GAAkBN,IAAI,CAACM,oCAAoC;AAQ5G;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,+BAA+B,GAAkBP,IAAI,CAACO,+BAA+B;AAQlG;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,sBAAsB,GAAkBR,IAAI,CAACQ,sBAAsB;AAQhF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,sBAAsB,GAAkBT,IAAI,CAACS,sBAAsB;AAuFhF;;;;;;;AAOA,OAAO,MAAMC,cAAc,GAAwDV,IAAI,CAACU,cAAc;AA4OtG;;;;;;;;;;;;;AAaA,OAAO,MAAMC,KAAK,GAAiBZ,QAAQ,CAACY,KAAK;AAEjD;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,IAAI,GAA8Bb,QAAQ,CAACa,IAAI;AAE5D;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,GAAG,GAAsCd,QAAQ,CAACc,GAAG;AAElE;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,SAAS,GAA+Cf,QAAQ,CAACe,SAAS;AAEvF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,QAAQ,GAA+DhB,QAAQ,CAACgB,QAAQ;AAErG;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,UAAU,GAA+DjB,QAAQ,CAACiB,UAAU;AAEzG;;;;;;AAMA,OAAO,MAAMC,OAAO,GAAwClB,QAAQ,CAACkB,OAAO;AAE5E;;;;;;;;AAQA,OAAO,MAAMC,WAAW,GAAyCnB,QAAQ,CAACmB,WAAW;AAErF;;;;;;;;AAQA,OAAO,MAAMC,UAAU,GAA2CpB,QAAQ,CAACoB,UAAU;AAErF;;;;;;;;AAQA,OAAO,MAAMC,SAAS,GAAuCrB,QAAQ,CAACqB,SAAS;AAE/E;;;;;;;;AAQA,OAAO,MAAMC,eAAe,GAA6CtB,QAAQ,CAACsB,eAAe;AAEjG;;;;;;;;AAQA,OAAO,MAAMC,gBAAgB,GAAiDvB,QAAQ,CAACuB,gBAAgB;AAEvG;;;;;;;;AAQA,OAAO,MAAMC,cAAc,GAA+CxB,QAAQ,CAACwB,cAAc;AAEjG;;;;;;;;;;;AAWA,OAAO,MAAMC,IAAI,GAAkCzB,QAAQ,CAACyB,IAAI;AAEhE;;;;;;;;;;;;AAYA,OAAO,MAAMC,OAAO,GAAmC1B,QAAQ,CAAC0B,OAAO;AAEvE;;;;;;;;;;;;AAYA,OAAO,MAAMC,SAAS,GAAmC3B,QAAQ,CAAC2B,SAAS;AAE3E;;;;;;;;;;;;AAYA,OAAO,MAAMC,KAAK,GAAmC5B,QAAQ,CAAC4B,KAAK;AAEnE;;;;;;;;;;AAUA,OAAO,MAAMC,aAAa,GAAmC7B,QAAQ,CAAC6B,aAAa;AAEnF;;;;;;;;;;;;AAYA,OAAO,MAAMC,iBAAiB,GAAmC9B,QAAQ,CAAC8B,iBAAiB;AAE3F;;;;;;;;;;;;AAYA,OAAO,MAAMC,QAAQ,GAA0C/B,QAAQ,CAAC+B,QAAQ;AAEhF;;;;;;;;;;;;AAYA,OAAO,MAAMC,OAAO,GAAgDhC,QAAQ,CAACgC,OAAO;AAEpF;;;;;;;;;;;;AAYA,OAAO,MAAMC,YAAY,GAA4DjC,QAAQ,CAACiC,YAAY;AAE1G;;;;;;;;;;;;AAYA,OAAO,MAAMC,aAAa,GAA4ClC,QAAQ,CAACkC,aAAa;AAE5F;;;;;;;;;;;;;AAaA,OAAO,MAAMC,cAAc,GAA0DnC,QAAQ,CAACmC,cAAc;AAE5G;;;;;;;;;;;;;AAaA,OAAO,MAAMC,eAAe,GAAkEpC,QAAQ,CAACoC,eAAe;AAEtH;;;;;;;;;;;;AAYA,OAAO,MAAMC,SAAS,GAAkDrC,QAAQ,CAACqC,SAAS;AAE1F;;;;;;;;;;;;AAYA,OAAO,MAAMC,eAAe,GAA0DtC,QAAQ,CAACsC,eAAe;AAE9G;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,WAAW,GAAuDvC,QAAQ,CAACuC,WAAW;AAEnG;AACA;;;;;;;;;;;;AAYA,OAAO,MAAMC,SAAS,GAAqDxC,QAAQ,CAACwC,SAAS;AAE7F;;;;;;;;;;;;;AAaA,OAAO,MAAMC,aAAa,GAAwCzC,QAAQ,CAACyC,aAAa;AAExF;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,gBAAgB,GA+BzB1C,QAAQ,CAAC0C,gBAAgB;AAE7B;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,EAAE,GA+BX3C,QAAQ,CAAC2C,EAAE;AAEf;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,GAAG,GA+BZ5C,QAAQ,CAAC4C,GAAG;AAEhB;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,OAAO,GA+BhB7C,QAAQ,CAAC6C,OAAO;AAEpB;;;;;;;AAOA,OAAO,MAAMC,OAAO,GAiChB9C,QAAQ,CAAC8C,OAAO;AAEpB;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,OAAO,GAA2C/C,QAAQ,CAAC+C,OAAO;AAE/E;;;;;;;;;;;;AAYA,OAAO,MAAMC,QAAQ,GA2BjBhD,QAAQ,CAACgD,QAAQ;AAErB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA,OAAO,MAAMC,MAAM,GAAmChD,IAAI,CAACiD,WAAW;AAEtE;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,UAAU,GAqDnBlD,IAAI,CAACmD,eAAe;AAExB;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAO,MAAMC,IAAI,GA6CbrD,QAAQ,CAACqD,IAAI;AAEjB;;;;;;;;;;;;;;;;;;;;;AAqBA,OAAO,MAAMC,MAAM,GAyFftD,QAAQ,CAACsD,MAAM;AAEnB;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,OAAO,MAAMC,KAAK,GA4EdvD,QAAQ,CAACuD,KAAK;AAElB;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,OAAO,MAAMC,MAAM,GAyDfxD,QAAQ,CAACwD,MAAM;AAEnB;;;;;;;;;;;;;;;;;;;;;;;AAuBA,OAAO,MAAMC,iBAAiB,GAiD1BzD,QAAQ,CAACyD,iBAAiB;AAE9B;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,oBAAoB,GAA8DzD,IAAI,CAACyD,oBAAoB;AAExH;;;;;;AAMA,OAAO,MAAMC,sBAAsB,GAA8C1D,IAAI,CAAC0D,sBAAsB;AAE5G;;;;;;;;;;;;AAYA,OAAO,MAAMC,wBAAwB,GACnC3D,IAAI,CAAC2D,wBAAwB;AAE/B;;;;;;AAMA,OAAO,MAAMC,0BAA0B,GAAkD5D,IAAI,CAAC4D,0BAA0B;AAExH;;;;;;;;;;;AAWA,OAAO,MAAMC,sBAAsB,GACjC7D,IAAI,CAAC6D,sBAAsB;AAE7B;;;;;;AAMA,OAAO,MAAMC,wBAAwB,GAAgD9D,IAAI,CAAC8D,wBAAwB;AAElH;;;;;;;;;;;;;AAaA,OAAO,MAAMC,gBAAgB,GAA0D/D,IAAI,CAAC+D,gBAAgB;AAE5G;;;;;;AAMA,OAAO,MAAMC,kBAAkB,GAA0ChE,IAAI,CAACgE,kBAAkB;AAEhG;;;;;;;;;;;;AAYA,OAAO,MAAMC,gBAAgB,GAA0DjE,IAAI,CAACiE,gBAAgB;AAE5G;;;;;;AAMA,OAAO,MAAMC,kBAAkB,GAA0ClE,IAAI,CAACkE,kBAAkB;AAEhG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,OAAO,MAAMC,gBAAgB,GAC3BnE,IAAI,CAACmE,gBAAgB;AAEvB;;;;;;AAMA,OAAO,MAAMC,kBAAkB,GAA0CpE,IAAI,CAACoE,kBAAkB;AAEhG;;;;;;;;;;;;;AAaA,OAAO,MAAMC,yBAAyB,GACpCrE,IAAI,CAACqE,yBAAyB;AAEhC;;;;;;AAMA,OAAO,MAAMC,2BAA2B,GACtCtE,IAAI,CAACsE,2BAA2B;AAElC;;;;;;;;;;;;;;;;;;AAkBA,OAAO,MAAMC,MAAM,GAEJxE,QAAQ,CAACwE,MAAM;AAY9B;;;;;;;;;;;;AAYA,OAAO,MAAMC,YAAY,GAA+CzE,QAAQ,CAACyE,YAAY;AAE7F;;;;;;;;;;;;;;AAcA,OAAO,MAAMC,aAAa,GAAqBzE,IAAI,CAAC0E,gBAAgB", "ignoreList": []}