import * as internal from "./internal/fiberRefs/patch.js";
/**
 * @since 2.0.0
 * @category constructors
 */
export const empty = internal.empty;
/**
 * Constructs a patch that describes the changes between the specified
 * collections of `FiberRef`
 *
 * @since 2.0.0
 * @category constructors
 */
export const diff = internal.diff;
/**
 * Combines this patch and the specified patch to create a new patch that
 * describes applying the changes from this patch and the specified patch
 * sequentially.
 *
 * @since 2.0.0
 * @category constructors
 */
export const combine = internal.combine;
/**
 * Applies the changes described by this patch to the specified collection
 * of `FiberRef` values.
 *
 * @since 2.0.0
 * @category destructors
 */
export const patch = internal.patch;
//# sourceMappingURL=FiberRefsPatch.js.map